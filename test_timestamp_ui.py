#!/usr/bin/env python3
"""
测试时间戳转换工具的UI改进
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def test_timestamp_ui():
    """测试时间戳转换工具的UI改进"""
    print("🚀 启动时间戳转换工具UI测试...")
    
    # 启动Flask应用
    try:
        print("📱 启动Flask应用...")
        process = subprocess.Popen([
            sys.executable, "app.py"
        ], cwd=Path(__file__).parent)
        
        # 等待服务器启动
        time.sleep(3)
        
        # 打开浏览器
        print("🌐 打开浏览器...")
        webbrowser.open("http://localhost:5000")
        
        print("\n✅ 测试要点:")
        print("1. 检查两个工具的转换结果样式是否完全一致")
        print("2. 点击清空按钮后，支持格式是否显示在转换结果区域")
        print("3. 进行转换后，支持格式是否消失")
        print("4. 再次点击清空，支持格式是否重新出现")
        print("5. 页面加载时是否自动显示支持格式")
        
        print("\n🎯 预期效果:")
        print("- 左右两个工具的转换结果样式完全一致")
        print("- 清空按钮会在转换结果区域显示支持格式")
        print("- 转换操作会替换支持格式显示为实际结果")
        print("- 支持格式在清空时重新出现")
        
        print("\n按 Ctrl+C 停止测试...")
        process.wait()
        
    except KeyboardInterrupt:
        print("\n🛑 停止测试")
        process.terminate()
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_timestamp_ui()
