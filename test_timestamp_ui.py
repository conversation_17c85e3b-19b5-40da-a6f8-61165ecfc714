#!/usr/bin/env python3
"""
测试时间戳转换工具的UI改进
"""

import subprocess
import sys
import time
import webbrowser
from pathlib import Path

def test_timestamp_ui():
    """测试时间戳转换工具的UI改进"""
    print("🚀 启动时间戳转换工具UI测试...")
    
    # 启动Flask应用
    try:
        print("📱 启动Flask应用...")
        process = subprocess.Popen([
            sys.executable, "app.py"
        ], cwd=Path(__file__).parent)
        
        # 等待服务器启动
        time.sleep(3)
        
        # 打开浏览器
        print("🌐 打开浏览器...")
        webbrowser.open("http://localhost:5000")
        
        print("\n✅ 测试要点:")
        print("1. 检查转换结果区域是否有滚动条（固定高度200px）")
        print("2. 转换结果是否使用纯文本格式显示（无复杂样式）")
        print("3. 文本是否醒目且有适当的行高")
        print("4. 内容过长时是否可以滚动查看")
        print("5. 清空按钮显示的支持格式是否也是纯文本")
        print("6. 页面加载时是否自动显示支持格式")

        print("\n🎯 预期效果:")
        print("- 转换结果区域固定高度，内容过长时显示滚动条")
        print("- 转换结果使用简洁的纯文本格式，易于阅读")
        print("- 文本有合适的行高和字体大小")
        print("- 支持格式和转换结果都使用一致的文本样式")
        print("- 滚动条仅在垂直方向显示，水平方向隐藏")
        
        print("\n按 Ctrl+C 停止测试...")
        process.wait()
        
    except KeyboardInterrupt:
        print("\n🛑 停止测试")
        process.terminate()
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_timestamp_ui()
