<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时间戳转换工具 - 滚动条和纯文本测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">时间戳转换工具 - 滚动条和纯文本测试</h1>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 时间戳转日期 -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold mb-4 text-blue-600">时间戳转日期</h2>
                
                <div class="mb-4">
                    <input type="text" id="ts-input" placeholder="输入时间戳..." 
                           class="w-full p-3 border border-gray-300 rounded-lg">
                </div>
                
                <div class="flex gap-2 mb-4">
                    <button onclick="convertTimestamp()" 
                            class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                        转换
                    </button>
                    <button onclick="clearTimestamp()" 
                            class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                        清空
                    </button>
                </div>
                
                <!-- 转换结果区域 - 固定高度，带滚动条 -->
                <div id="ts-output" 
                     class="w-full p-4 bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-gray-200 rounded-xl text-sm h-[200px] whitespace-pre-wrap font-mono overflow-y-auto overflow-x-hidden">
                    <!-- 结果将在这里显示 -->
                </div>
            </div>
            
            <!-- 日期转时间戳 -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold mb-4 text-green-600">日期转时间戳</h2>
                
                <div class="mb-4">
                    <input type="text" id="date-input" placeholder="输入日期时间..." 
                           class="w-full p-3 border border-gray-300 rounded-lg">
                </div>
                
                <div class="flex gap-2 mb-4">
                    <button onclick="convertDate()" 
                            class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">
                        转换
                    </button>
                    <button onclick="clearDate()" 
                            class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600">
                        清空
                    </button>
                </div>
                
                <!-- 转换结果区域 - 固定高度，带滚动条 -->
                <div id="date-output" 
                     class="w-full p-4 bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-gray-200 rounded-xl text-sm h-[200px] whitespace-pre-wrap font-mono overflow-y-auto overflow-x-hidden">
                    <!-- 结果将在这里显示 -->
                </div>
            </div>
        </div>
        
        <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-lg font-bold mb-4 text-gray-800">测试说明</h3>
            <ul class="list-disc list-inside space-y-2 text-gray-600">
                <li>转换结果区域固定高度为200px</li>
                <li>内容过长时会显示垂直滚动条</li>
                <li>转换结果使用纯文本格式，无复杂样式</li>
                <li>文本有适当的行高和字体大小</li>
                <li>清空按钮会显示支持格式说明</li>
            </ul>
        </div>
    </div>

    <script>
        // 模拟转换结果
        function convertTimestamp() {
            const input = document.getElementById('ts-input');
            const output = document.getElementById('ts-output');
            
            if (!input.value.trim()) {
                alert('请输入时间戳');
                return;
            }
            
            // 模拟长文本结果
            const resultText = `识别类型: 秒级时间戳

本地时间: 2025-06-27 19:40:25

中文格式: 2025年06月27日 星期五 19:40:25

秒级时间戳: 1751035225

毫秒级时间戳: 1751035225000

额外信息:
- UTC时间: 2025-06-27 11:40:25
- 时区: GMT+8
- 星期: 星期五
- 年份: 2025
- 月份: 06
- 日期: 27
- 小时: 19
- 分钟: 40
- 秒数: 25
- 这是一个很长的文本，用来测试滚动条是否正常工作
- 当内容超过容器高度时，应该出现垂直滚动条
- 用户可以通过滚动查看所有内容`;

            output.innerHTML = `<div class="text-gray-800 leading-relaxed font-medium whitespace-pre-line text-base">${resultText}</div>`;
        }
        
        function convertDate() {
            const input = document.getElementById('date-input');
            const output = document.getElementById('date-output');
            
            if (!input.value.trim()) {
                alert('请输入日期时间');
                return;
            }
            
            // 模拟长文本结果
            const resultText = `原始输入: ${input.value}

解析结果: 2025-06-27 19:40:25

秒级时间戳: 1751035225

毫秒级时间戳: 1751035225000

详细信息:
- 解析成功
- 时区: 本地时区 (GMT+8)
- 格式: 标准日期时间格式
- 有效性: 有效日期
- 转换精度: 秒级
- 这也是一个很长的文本内容
- 用来测试右侧转换结果区域的滚动功能
- 确保两个区域的样式和行为完全一致
- 文本应该清晰易读，有适当的行高`;

            output.innerHTML = `<div class="text-gray-800 leading-relaxed font-medium whitespace-pre-line text-base">${resultText}</div>`;
        }
        
        function clearTimestamp() {
            const input = document.getElementById('ts-input');
            const output = document.getElementById('ts-output');
            
            input.value = '';
            
            const supportText = `支持格式:

10位数字 → 秒级时间戳
13位数字 → 毫秒级时间戳  
16位数字 → 微秒级时间戳

示例:
1751035513 (秒级)
1751035513249 (毫秒级)`;

            output.innerHTML = `<div class="text-gray-600 leading-relaxed font-medium whitespace-pre-line text-base">${supportText}</div>`;
        }
        
        function clearDate() {
            const input = document.getElementById('date-input');
            const output = document.getElementById('date-output');
            
            input.value = '';
            
            const supportText = `支持格式:

完整格式: 2025-06-27 19:40:25
时分格式: 2025-06-27 19:40
仅日期: 2025-06-27
年月格式: 2025-06

示例:
2025-06-27 19:40:25
2025-06-27
2025-06`;

            output.innerHTML = `<div class="text-gray-600 leading-relaxed font-medium whitespace-pre-line text-base">${supportText}</div>`;
        }
        
        // 页面加载时显示支持格式
        window.onload = function() {
            clearTimestamp();
            clearDate();
        };
    </script>
</body>
</html>
