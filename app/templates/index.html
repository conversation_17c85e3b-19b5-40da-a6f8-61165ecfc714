<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>爬虫工具箱</title>
    <!-- 依赖库 -->
     <style>
        .search-highlight {
            background-color: yellow;
            color: black;
        }
        .current-match {
            background-color: orange !important;
            border: 1px solid red;
            border-radius: 2px;
        }

        /* 时间戳工具美化样式 */
        .timestamp-card {
            transition: all 0.3s ease;
        }

        .timestamp-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .pulse-dot {
            animation: pulse-dot 2s infinite;
        }

        @keyframes pulse-dot {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .success-animation {
            animation: success-bounce 0.6s ease-out;
        }

        @keyframes success-bounce {
            0% { transform: scale(0.8); opacity: 0; }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); opacity: 1; }
        }

        .loading-shimmer {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        /* 文本对比工具样式 */
        .diff-line {
            padding: 2px 8px;
            margin: 1px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            word-break: break-all;
        }
        .diff-line.equal {
            background-color: transparent;
        }
        .diff-line.deleted {
            background-color: #fee2e2;
            color: #dc2626;
            border-left: 4px solid #dc2626;
        }
        .diff-line.added {
            background-color: #dcfce7;
            color: #16a34a;
            border-left: 4px solid #16a34a;
        }
        .line-number {
            display: inline-block;
            width: 40px;
            text-align: right;
            margin-right: 8px;
            color: #9ca3af;
            user-select: none;
        }

        /* JSON格式化器样式 - 紧凑现代风格 */
        .json-line {
            line-height: 1.4;
            font-family: 'Courier New', 'Monaco', monospace;
            font-size: 13px;
            white-space: pre;
        }

        .json-toggle {
            cursor: pointer;
            user-select: none;
            margin-right: 4px;
        }

        .json-toggle:hover .json-toggle-icon {
            color: #3b82f6 !important;
        }

        .json-toggle-icon {
            color: #9ca3af;
            font-size: 12px;
            transition: color 0.2s;
        }

        .json-bracket {
            color: #6b7280;
            font-weight: bold;
        }

        .json-key {
            color: #059669;
            font-weight: 500;
        }

        .json-string {
            color: #dc2626;
        }

        .json-number {
            color: #2563eb;
            font-weight: 500;
        }

        .json-boolean {
            color: #7c3aed;
            font-weight: 500;
        }

        .json-null {
            color: #7c3aed;
            font-weight: 500;
            font-style: italic;
        }

        .json-count {
            font-size: 11px;
            color: #9ca3af;
            font-weight: normal;
        }

        .json-content {
            transition: all 0.2s ease;
        }

        .json-content.collapsed {
            display: none;
        }


        /* 文本对比工具样式 */
        .text-compare-line {
            min-height: 24px;
            line-height: 24px;
            padding: 0 8px;
            border-radius: 4px;
            margin: 1px 0;
            position: relative;
        }

        .text-compare-line.equal {
            background-color: transparent;
        }

        .text-compare-line.added {
            background-color: #dcfce7;
            border-left: 4px solid #22c55e;
        }

        .text-compare-line.deleted {
            background-color: #fef2f2;
            border-left: 4px solid #ef4444;
        }

        .text-compare-line.modified {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
        }

        .line-number {
            display: inline-block;
            width: 100%;
            padding: 2px 8px;
            text-align: right;
            cursor: pointer;
            transition: background-color 0.2s;
            position: relative;
        }

        .line-number:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }

        .line-number.has-diff {
            background-color: rgba(239, 68, 68, 0.1);
        }

        .line-number.has-diff:hover {
            background-color: rgba(239, 68, 68, 0.2);
        }

        .arrow-icon {
            position: absolute;
            right: 2px;
            top: 50%;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            background: #ef4444;
            color: white;
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8px;
            cursor: pointer;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .arrow-icon:hover {
            opacity: 1;
        }

        .arrow-icon.arrow-right::before {
            content: '→';
        }

        .arrow-icon.arrow-left::before {
            content: '←';
        }

        #text-a-editor, #text-b-editor {
            outline: none;
            border: none;
            resize: none;
        }

        #text-a-editor:focus, #text-b-editor:focus {
            outline: none;
        }

        /* 文本转换工具选项卡样式 */
        .text-convert-tab-btn {
            border-bottom: 2px solid transparent;
            color: #6b7280;
            transition: all 0.2s;
            cursor: pointer;
        }

        .text-convert-tab-btn:hover {
            color: #374151;
            background-color: #f3f4f6;
        }

        .text-convert-tab-btn.active {
            color: #2563eb;
            border-bottom-color: #2563eb;
            background-color: #eff6ff;
        }

        .text-convert-tab-content {
            display: block;
        }

        .text-convert-tab-content.tab-hidden {
            display: none;
        }

        /* 编码检测结果样式 */
        .encoding-info {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-left: 4px solid #0ea5e9;
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 8px;
        }

        .encoding-confidence {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .confidence-high {
            background-color: #dcfce7;
            color: #166534;
        }

        .confidence-medium {
            background-color: #fef3c7;
            color: #92400e;
        }

        .confidence-low {
            background-color: #fee2e2;
            color: #991b1b;
        }

        /* Unicode字符信息样式 */
        .unicode-char-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 8px;
            margin-bottom: 6px;
            font-family: 'Courier New', monospace;
        }

        .unicode-char-item .char {
            font-size: 18px;
            font-weight: bold;
            color: #7c3aed;
        }

        .unicode-char-item .code {
            color: #059669;
            font-size: 12px;
        }

        .unicode-char-item .name {
            color: #6b7280;
            font-size: 11px;
        }

        /* 父子级导航样式 */
        .nav-parent-group {
            margin-bottom: 4px;
        }

        .nav-parent-header {
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .nav-parent-header:hover {
            background-color: #f3f4f6;
        }

        .nav-parent-header.active {
            background-color: #eff6ff;
            color: #2563eb;
        }

        .nav-parent-header:focus {
            outline: none !important;
            box-shadow: none !important;
            border: none !important;
        }

        .nav-parent-header:focus-visible {
            outline: none !important;
            box-shadow: none !important;
            border: none !important;
        }

        .nav-parent-arrow {
            font-size: 12px;
            transition: transform 0.2s ease;
        }

        .nav-parent-arrow.rotated {
            transform: rotate(-90deg);
        }

        .nav-children {
            overflow: hidden;
            transition: all 0.3s ease;
            max-height: 200px;
        }

        .nav-children.collapsed {
            max-height: 0;
            opacity: 0;
        }

        .nav-children .sidebar-link {
            font-size: 13px;
            padding-left: 2rem;
            border-left: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .nav-children .sidebar-link:hover {
            border-left-color: #e5e7eb;
            background-color: #f9fafb;
        }

        .nav-children .sidebar-link.active {
            background-color: #eff6ff;
            border-left-color: #3b82f6;
            color: #2563eb;
            font-weight: 500;
        }
    </style>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <!-- 外部自定义样式 -->
    <link rel="stylesheet" href="/static/css/style.css">
</head>
<body class="bg-gray-100 font-sans flex h-screen antialiased">

    <!-- 侧边导航栏 -->
    <aside class="w-60 bg-white shadow-md flex flex-col flex-shrink-0">
        <div class="p-4 border-b"><h1 class="text-xl font-bold text-gray-800">爬虫工具箱</h1></div>
        <nav id="sidebar-nav" class="flex-1 p-2 space-y-1">
            <a href="#" data-tool="json-tool" class="sidebar-link active flex items-center px-4 py-2 text-sm rounded-md text-gray-700">
                <i class="fas fa-code mr-2 text-green-500 w-4"></i>JSON格式化
            </a>
            <a href="#" data-tool="headers-tool" class="sidebar-link flex items-center px-4 py-2 text-sm rounded-md text-gray-700">
                <i class="fas fa-list mr-2 text-green-500 w-4"></i>Headers格式化
            </a>
            <a href="#" data-tool="xml-tool" class="sidebar-link flex items-center px-4 py-2 text-sm rounded-md text-gray-700">
                <i class="fas fa-code mr-2 text-orange-500 w-4"></i>XML格式化
            </a>
            <a href="#" data-tool="html-format-tool" class="sidebar-link flex items-center px-4 py-2 text-sm rounded-md text-gray-700">
                <i class="fas fa-file-code mr-2 text-red-500 w-4"></i>HTML格式化
            </a>
            <a href="#" data-tool="html-render-tool" class="sidebar-link flex items-center px-4 py-2 text-sm rounded-md text-gray-700">
                <i class="fas fa-eye mr-2 text-purple-500 w-4"></i>HTML渲染
            </a>
            <a href="#" data-tool="url-tool" class="sidebar-link flex items-center px-4 py-2 text-sm rounded-md text-gray-700">
                <i class="fas fa-link mr-2 text-teal-500 w-4"></i>URL参数提取
            </a>


            <!-- 编码解码父级目录 -->
            <div class="nav-parent-group">
                <div class="nav-parent-header flex items-center justify-between px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md cursor-pointer" data-parent="encoding-tools">
                    <span class="flex items-center">
                        <i class="fas fa-code mr-2 text-indigo-500"></i>
                        编码解码
                    </span>
                    <i class="fas fa-chevron-down nav-parent-arrow transition-transform duration-200"></i>
                </div>
                <div class="nav-children ml-4 space-y-1" data-parent-children="encoding-tools">
                    <a href="#" data-tool="base64-tool" class="sidebar-link flex items-center px-4 py-2 text-sm rounded-md text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                        <i class="fas fa-key mr-2 text-blue-500 w-4"></i>Base64编码解码
                    </a>
                    <a href="#" data-tool="html-entity-tool" class="sidebar-link flex items-center px-4 py-2 text-sm rounded-md text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                        <i class="fas fa-code mr-2 text-orange-500 w-4"></i>HTML编码解码
                    </a>
                    <a href="#" data-tool="url-encode-tool" class="sidebar-link flex items-center px-4 py-2 text-sm rounded-md text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                        <i class="fas fa-link mr-2 text-green-500 w-4"></i>URL编码解码
                    </a>
                    <a href="#" data-tool="unicode-tool" class="sidebar-link flex items-center px-4 py-2 text-sm rounded-md text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                        <i class="fas fa-language mr-2 text-purple-500 w-4"></i>Unicode编码解码
                    </a>
                </div>
            </div>

            <!-- 时间转换父级目录 -->
            <div class="nav-parent-group">
                <div class="nav-parent-header flex items-center justify-between px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md cursor-pointer" data-parent="datetime-tools">
                    <span class="flex items-center">
                        <i class="fas fa-clock mr-2 text-blue-500"></i>
                        时间转换
                    </span>
                    <i class="fas fa-chevron-down nav-parent-arrow transition-transform duration-200"></i>
                </div>
                <div class="nav-children ml-4 space-y-1" data-parent-children="datetime-tools">
                    <a href="#" data-tool="timestamp-tool" class="sidebar-link flex items-center px-4 py-2 text-sm rounded-md text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                        <i class="fas fa-exchange-alt mr-2 text-blue-500 w-4"></i>时间戳 ↔ 日期
                    </a>
                    <a href="#" data-tool="date-calculator-tool" class="sidebar-link flex items-center px-4 py-2 text-sm rounded-md text-gray-600 hover:text-gray-800 hover:bg-gray-50">
                        <i class="fas fa-calculator mr-2 text-green-500 w-4"></i>日期计算器
                    </a>
                </div>
            </div>
            <a href="#" data-tool="text-compare-tool" class="sidebar-link flex items-center px-4 py-2 text-sm rounded-md text-gray-700">
                <i class="fas fa-code-compare mr-2 text-pink-500 w-4"></i>文本对比
            </a>
        </nav>
    </aside>

    <!-- 主内容区 -->
    <div class="flex-1 flex flex-col overflow-hidden">
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-100 p-4 md:p-8">

            <!-- JSON 格式化工具 -->
            <div id="json-tool" class="tool-content flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">JSON 格式化工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="format-json-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-magic w-4 h-4 mr-1"></i>格式化
                                </button>
                                <button id="clear-json-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-json" class="tool-btn text-blue-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">常规示例</button>
                                <button id="load-sortable-json" class="tool-btn text-green-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">排序示例</button>
                                <button id="load-nested-json" class="tool-btn text-purple-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">嵌套示例</button>
                            </div>
                        </div>
                        <textarea id="raw-json" class="flex-1 w-full p-3 border-l border-r border-b bg-gray-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此粘贴您的JSON数据..."></textarea>
                        <div id="json-input-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>
                    <div class="w-1/2 flex flex-col">
                        <div id="json-toolbar" class="flex items-center space-x-3 bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                           <button data-action="search" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-search w-4 h-4 mr-1"></i>搜索</button>
                           <button data-action="collapse" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-compress-arrows-alt w-4 h-4 mr-1"></i>折叠/展开</button>
                           <button data-action="escape" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-exchange-alt w-4 h-4 mr-1"></i>转义</button>
                           <button data-action="sort" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-sort-alpha-down w-4 h-4 mr-1"></i>排序</button>
                           <button data-action="compress" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-align-left w-4 h-4 mr-1"></i>压缩</button>
                           <button data-action="copy" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-copy w-4 h-4 mr-1"></i>复制</button>
                        </div>

                    <div class="search-bar-container hidden items-center space-x-2 bg-gray-200 p-1 border-b border-gray-300">
                            <input type="text" placeholder="搜索..." class="search-input flex-grow p-1 rounded-sm border-gray-300 focus:ring-blue-500 focus:border-blue-500 text-sm">
                            <span class="search-count text-sm text-gray-700 font-mono">0 / 0</span>
                            <div class="flex items-center">
                                <button class="search-prev-btn p-1 hover:bg-gray-300 rounded-sm disabled:opacity-50" title="上一个"><i class="fas fa-arrow-up fa-fw"></i></button>
                                <button class="search-next-btn p-1 hover:bg-gray-300 rounded-sm disabled:opacity-50" title="下一个"><i class="fas fa-arrow-down fa-fw"></i></button>
                            </div>
                            <button class="search-close-btn p-1 hover:bg-gray-300 rounded-sm" title="关闭"><i class="fas fa-times fa-fw"></i></button>
                        </div>



                        <div class="relative flex-1 overflow-hidden">
                            <pre><code id="json-output" data-plain-text="" class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r border-b rounded-b-lg font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                        </div>
                        <div id="json-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>

            <!-- Headers 格式化工具 -->
            <div id="headers-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">请求头 Headers 格式化工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: 输入区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="convert-headers-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-magic w-4 h-4 mr-1"></i>转换格式
                                </button>
                                <button id="clear-headers-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-headers" class="tool-btn text-blue-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">载入示例</button>
                            </div>
                        </div>
                        <textarea id="raw-headers" class="flex-1 w-full p-3 border-l border-r border-b bg-gray-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此粘贴从浏览器复制的原始Headers..."></textarea>
                        <div id="headers-input-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>

                    <!-- 右侧: 双输出区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 右侧工具栏 -->
                        <div class="flex items-center space-x-3 bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <button data-action="copy-headers" class="tool-btn hover:bg-gray-200 p-1 rounded">
                                <i class="fas fa-copy w-4 h-4 mr-1"></i>复制Headers
                            </button>
                            <button data-action="copy-cookies" class="tool-btn hover:bg-gray-200 p-1 rounded">
                                <i class="fas fa-cookie-bite w-4 h-4 mr-1"></i>复制Cookies
                            </button>
                        </div>

                        <!-- Headers输出区 (60%高度) -->
                        <div class="flex-1 flex flex-col" style="flex: 3;">
                            <div class="bg-gray-50 px-3 py-1 border-l border-r text-xs text-gray-600 font-medium">
                                Python Headers 字典:
                            </div>
                            <div class="relative flex-1 overflow-hidden">
                                <pre><code id="headers-output" data-plain-text="" class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                            </div>
                        </div>

                        <!-- Cookies输出区 (40%高度) -->
                        <div class="flex-1 flex flex-col" style="flex: 2;">
                            <div class="bg-gray-50 px-3 py-1 border-l border-r border-t text-xs text-gray-600 font-medium">
                                Python Cookies 字典:
                            </div>
                            <div class="relative flex-1 overflow-hidden">
                                <pre><code id="cookies-output" data-plain-text="" class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r border-b rounded-b-lg font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                            </div>
                        </div>

                        <div id="headers-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>

            <!-- XML 格式化工具 -->
            <div id="xml-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">XML 格式化工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: 输入区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="format-xml-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-magic w-4 h-4 mr-1"></i>格式化
                                </button>
                                <button id="clear-xml-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-xml" class="tool-btn text-blue-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">载入示例</button>
                            </div>
                        </div>
                        <textarea id="raw-xml" class="flex-1 w-full p-3 border-l border-r border-b bg-gray-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此粘贴您的XML数据..."></textarea>
                        <div id="xml-input-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>
                    <!-- 右侧: 输出区 -->
                    <div class="w-1/2 flex flex-col">
                        <div id="xml-toolbar" class="flex items-center space-x-3 bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                           <button data-action="search" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-search w-4 h-4 mr-1"></i>搜索</button>
                           <button data-action="compress" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-align-left w-4 h-4 mr-1"></i>压缩</button>
                           <button data-action="copy" class="tool-btn hover:bg-gray-200 p-1 rounded"><i class="fas fa-copy w-4 h-4 mr-1"></i>复制</button>
                        </div>

                        <div class="search-bar-container hidden items-center space-x-2 bg-gray-200 p-1 border-b border-gray-300">
                            <input type="text" placeholder="搜索..." class="search-input flex-grow p-1 rounded-sm border-gray-300 focus:ring-blue-500 focus:border-blue-500 text-sm">
                            <span class="search-count text-sm text-gray-700 font-mono">0 / 0</span>
                            <div class="flex items-center">
                                <button class="search-prev-btn p-1 hover:bg-gray-300 rounded-sm disabled:opacity-50" title="上一个"><i class="fas fa-arrow-up fa-fw"></i></button>
                                <button class="search-next-btn p-1 hover:bg-gray-300 rounded-sm disabled:opacity-50" title="下一个"><i class="fas fa-arrow-down fa-fw"></i></button>
                            </div>
                            <button class="search-close-btn p-1 hover:bg-gray-300 rounded-sm" title="关闭"><i class="fas fa-times fa-fw"></i></button>
                        </div>


                        <div class="relative flex-1 overflow-hidden">
                            <pre><code id="xml-output" data-plain-text="" class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r border-b rounded-b-lg font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                        </div>
                        <div id="xml-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>


            <div id="html-format-tool"
                 class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">HTML 格式化工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="format-html-button" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-magic w-4 h-4 mr-1"></i>格式化
                                </button>
                                <button id="clear-html-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-html-btn" class="tool-btn text-blue-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">载入示例</button>
                            </div>
                        </div>
                        <textarea id="raw-html-input"
                                  class="flex-1 w-full p-3 border-l border-r border-b bg-gray-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm"
                                  placeholder="在此粘贴您的HTML数据..."></textarea>
                        <div id="html-input-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>


                    <div class="w-1/2 flex flex-col">
                        <div id="html-toolbar"
                             class="flex items-center space-x-3 bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">

                             <button data-action="search" class="tool-btn hover:bg-gray-200 p-1 rounded"><i
                                    class="fas fa-search w-4 h-4 mr-1"></i>搜索
                            </button>

                            <button data-action="copy" class="tool-btn hover:bg-gray-200 p-1 rounded"><i
                                    class="fas fa-copy w-4 h-4 mr-1"></i>复制
                            </button>
                        </div>


                          <div class="search-bar-container hidden items-center space-x-2 bg-gray-200 p-1 border-b border-gray-300">
                            <input type="text" placeholder="搜索..." class="search-input flex-grow p-1 rounded-sm border-gray-300 focus:ring-blue-500 focus:border-blue-500 text-sm">
                            <span class="search-count text-sm text-gray-700 font-mono">0 / 0</span>
                            <div class="flex items-center">
                                <button class="search-prev-btn p-1 hover:bg-gray-300 rounded-sm disabled:opacity-50" title="上一个"><i class="fas fa-arrow-up fa-fw"></i></button>
                                <button class="search-next-btn p-1 hover:bg-gray-300 rounded-sm disabled:opacity-50" title="下一个"><i class="fas fa-arrow-down fa-fw"></i></button>
                            </div>
                            <button class="search-close-btn p-1 hover:bg-gray-300 rounded-sm" title="关闭"><i class="fas fa-times fa-fw"></i></button>
                        </div>


                        <div class="relative flex-1 overflow-hidden">
                            <pre><code id="html-output" data-plain-text=""
                                       class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r border-b rounded-b-lg font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                        </div>
                        <div id="html-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>

           <div id="html-render-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">HTML 渲染工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: HTML输入区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="render-html-btn" class="tool-btn bg-green-600 text-white hover:bg-green-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-play w-4 h-4 mr-1"></i>渲染HTML
                                </button>
                                <button id="clear-html-render-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-render-btn" class="tool-btn text-blue-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">载入示例</button>
                            </div>
                        </div>
                        <textarea id="html-render-input" class="flex-1 w-full p-3 border-l border-r border-b bg-gray-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此粘贴HTML源代码..."></textarea>
                        <div id="html-render-input-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>

                    <!-- 右侧: 渲染结果区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 右侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <span class="font-medium">渲染结果</span>
                            <div class="flex items-center space-x-2">
                                <span class="text-xs text-gray-500">安全沙箱环境</span>
                                <button id="refresh-render-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-sync-alt w-3 h-3 mr-1"></i>刷新
                                </button>
                            </div>
                        </div>
                        <div class="flex-1 border-l border-r border-b bg-white rounded-b-lg overflow-hidden">
                            <iframe id="html-render-output" class="w-full h-full bg-white" sandbox="allow-same-origin"></iframe>
                        </div>
                        <div id="html-render-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>

            <div id="url-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">URL 参数提取工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: 输入区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="parse-url-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-magic w-4 h-4 mr-1"></i>提取参数
                                </button>
                                <button id="clear-url-btn" class="tool-btn hover:bg-gray-200 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-example-url-btn" class="tool-btn text-blue-600 hover:bg-gray-200 px-2 py-1 rounded text-xs">载入示例</button>
                            </div>
                        </div>
                        <textarea id="raw-url-input" class="flex-1 w-full p-3 border-l border-r border-b bg-gray-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此粘贴完整的URL链接..."></textarea>
                        <div id="url-input-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>

                    <!-- 右侧: 双输出区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 右侧工具栏 -->
                        <div class="flex items-center space-x-3 bg-gray-100 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <button data-action="copy-base-url" class="tool-btn hover:bg-gray-200 p-1 rounded">
                                <i class="fas fa-link w-4 h-4 mr-1"></i>复制URL
                            </button>
                            <button data-action="copy-params" class="tool-btn hover:bg-gray-200 p-1 rounded">
                                <i class="fas fa-list w-4 h-4 mr-1"></i>复制参数
                            </button>
                        </div>

                        <!-- 基础URL输出区 (30%高度) -->
                        <div class="flex-1 flex flex-col" style="flex: 1.5;">
                            <div class="bg-gray-50 px-3 py-1 border-l border-r text-xs text-gray-600 font-medium">
                                基础 URL:
                            </div>
                            <div class="relative flex-1 overflow-hidden">
                                <pre><code id="base-url-output" data-plain-text="" class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                            </div>
                        </div>

                        <!-- 参数输出区 (70%高度) -->
                        <div class="flex-1 flex flex-col" style="flex: 3.5;">
                            <div class="bg-gray-50 px-3 py-1 border-l border-r border-t text-xs text-gray-600 font-medium">
                                参数 (Params) 字典:
                            </div>
                            <div class="relative flex-1 overflow-hidden">
                                <pre><code id="params-output" data-plain-text="" class="absolute inset-0 w-full h-full bg-gray-50 p-3 border-l border-r border-b rounded-b-lg font-mono text-sm overflow-auto whitespace-pre-wrap leading-relaxed"></code></pre>
                            </div>
                        </div>

                        <div id="url-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>

            <div id="base64-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">Base64 编码解码工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: 明文输入区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-blue-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="base64-encode-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-arrow-down w-4 h-4 mr-1"></i>编码
                                </button>
                                <button id="clear-base64-plain-btn" class="tool-btn hover:bg-blue-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="copy-base64-plain-btn" class="tool-btn text-blue-600 hover:bg-blue-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-copy w-3 h-3 mr-1"></i>复制
                                </button>
                                <button id="load-base64-example-btn" class="tool-btn text-blue-600 hover:bg-blue-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-download w-3 h-3 mr-1"></i>载入示例
                                </button>
                            </div>
                        </div>
                        <div class="bg-blue-50 px-3 py-1 border-l border-r text-xs text-blue-700 font-medium">
                            明文 (Plain Text):
                        </div>
                        <textarea id="base64-plain-input" class="flex-1 w-full p-3 border-l border-r border-b bg-blue-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此输入要编码的明文..."></textarea>
                        <div id="base64-plain-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>

                    <!-- 右侧: Base64输出区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 右侧工具栏 -->
                        <div class="flex items-center justify-between bg-green-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="base64-decode-btn" class="tool-btn bg-green-600 text-white hover:bg-green-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-arrow-up w-4 h-4 mr-1"></i>解码
                                </button>
                                <button id="clear-base64-encoded-btn" class="tool-btn hover:bg-green-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="copy-base64-encoded-btn" class="tool-btn text-green-600 hover:bg-green-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-copy w-3 h-3 mr-1"></i>复制
                                </button>
                                <button id="load-base64-encoded-example-btn" class="tool-btn text-green-600 hover:bg-green-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-download w-3 h-3 mr-1"></i>载入示例
                                </button>
                            </div>
                        </div>
                        <div class="bg-green-50 px-3 py-1 border-l border-r text-xs text-green-700 font-medium">
                            Base64 字符串:
                        </div>
                        <textarea id="base64-b64-input" class="flex-1 w-full p-3 border-l border-r border-b bg-green-50 rounded-b-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-200 resize-none font-mono text-sm" placeholder="在此输入要解码的Base64字符串..."></textarea>
                        <div id="base64-encoded-message-area" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>
                </div>
                <div id="base64-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
            </div>

       <!-- 时间戳转换工具 -->
       <div id="timestamp-tool" class="tool-content hidden flex flex-col h-full bg-gradient-to-br from-blue-50 to-indigo-100 p-6 rounded-xl shadow-2xl">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-2">
                        <i class="fas fa-clock mr-3"></i>时间戳 ↔ 日期工具
                    </h2>
                </div>


                <div class="flex-1 flex space-x-8 overflow-hidden">
                    <!-- 左侧: 时间戳转日期 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 现代化卡片容器 -->
                        <div class="bg-white rounded-2xl shadow-xl overflow-hidden border border-blue-100">
                            <!-- 卡片头部 -->
                            <div class="bg-gradient-to-r from-blue-500 to-blue-600 p-4 text-white">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-hashtag text-lg"></i>
                                        <h3 class="font-semibold text-lg">时间戳转日期</h3>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button id="load-timestamp-example-btn" class="bg-white/20 hover:bg-white/30 px-3 py-1 rounded-lg text-sm transition-all duration-200 backdrop-blur-sm">
                                            <i class="fas fa-magic mr-1"></i>示例
                                        </button>
                                        <button id="clear-timestamp-btn" class="bg-white/20 hover:bg-white/30 px-3 py-1 rounded-lg text-sm transition-all duration-200 backdrop-blur-sm">
                                            <i class="fas fa-broom mr-1"></i>清空
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 实时时间戳显示区域 - 美化版 -->
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-b border-blue-100">
                                <div class="grid grid-cols-1 gap-3">
                                    <div class="flex items-center justify-between bg-white rounded-lg p-3 shadow-sm border border-blue-100">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                                            <span class="text-blue-700 font-medium text-sm">秒级时间戳</span>
                                        </div>
                                        <span id="current-timestamp-seconds" class="font-mono text-blue-800 font-bold text-lg bg-blue-50 px-3 py-1 rounded-md cursor-pointer hover:bg-blue-100 transition-colors" title="点击复制"></span>
                                    </div>
                                    <div class="flex items-center justify-between bg-white rounded-lg p-3 shadow-sm border border-indigo-100">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-indigo-500 rounded-full animate-pulse"></div>
                                            <span class="text-indigo-700 font-medium text-sm">毫秒级时间戳</span>
                                        </div>
                                        <span id="current-timestamp-milliseconds" class="font-mono text-indigo-800 font-bold text-lg bg-indigo-50 px-3 py-1 rounded-md cursor-pointer hover:bg-indigo-100 transition-colors" title="点击复制"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- 输入区域 -->
                            <div class="p-6">
                                <div class="space-y-4">
                                    <div>
                                        <label for="ts-to-date-input" class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                                            <i class="fas fa-keyboard mr-2 text-blue-500"></i>输入时间戳
                                        </label>
                                        <input type="text" id="ts-to-date-input"
                                               class="w-full p-4 border-2 border-gray-200 rounded-xl font-mono text-lg focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-200 bg-gray-50 hover:bg-white"
                                               placeholder="粘贴时间戳...">
                                    </div>

                                    <!-- 转换按钮 -->
                                    <button id="ts-to-date-btn" class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2">
                                        <i class="fas fa-exchange-alt"></i>
                                        <span>转换</span>
                                        <div class="hidden" id="ts-loading">
                                            <i class="fas fa-spinner fa-spin"></i>
                                        </div>
                                    </button>

                                    <div>
                                        <label class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                                            <i class="fas fa-calendar-alt mr-2 text-green-500"></i>转换结果
                                        </label>
                                        <div id="ts-to-date-output" class="w-full p-4 bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-gray-200 rounded-xl text-sm min-h-[120px] whitespace-pre-wrap font-mono overflow-auto">
                                            <div class="text-center py-8 text-gray-500">
                                                <i class="fas fa-info-circle text-2xl mb-2"></i>
                                                <div class="text-sm">支持格式：10位(秒)、13位(毫秒)、16位(微秒)时间戳</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 格式支持说明 - 美化版 -->
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 border-t border-blue-100">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-info-circle text-blue-500 mr-2"></i>
                                    <span class="font-semibold text-blue-700 text-sm">支持格式</span>
                                </div>
                                <div class="grid grid-cols-1 gap-2 text-xs">
                                    <div class="flex items-center space-x-2 bg-white rounded-lg p-2 border border-blue-100">
                                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <span class="text-gray-700"><strong>10位:</strong> 秒级时间戳 (1640995200)</span>
                                    </div>
                                    <div class="flex items-center space-x-2 bg-white rounded-lg p-2 border border-blue-100">
                                        <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                        <span class="text-gray-700"><strong>13位:</strong> 毫秒级时间戳 (1640995200000)</span>
                                    </div>
                                    <div class="flex items-center space-x-2 bg-white rounded-lg p-2 border border-blue-100">
                                        <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                                        <span class="text-gray-700"><strong>16位:</strong> 微秒级时间戳 (1640995200000000)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧: 日期转时间戳 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 现代化卡片容器 -->
                        <div class="bg-white rounded-2xl shadow-xl overflow-hidden border border-green-100">
                            <!-- 卡片头部 -->
                            <div class="bg-gradient-to-r from-green-500 to-emerald-600 p-4 text-white">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-calendar-day text-lg"></i>
                                        <h3 class="font-semibold text-lg">日期转时间戳</h3>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <button id="load-date-example-btn" class="bg-white/20 hover:bg-white/30 px-3 py-1 rounded-lg text-sm transition-all duration-200 backdrop-blur-sm">
                                            <i class="fas fa-magic mr-1"></i>示例
                                        </button>
                                        <button id="clear-date-btn" class="bg-white/20 hover:bg-white/30 px-3 py-1 rounded-lg text-sm transition-all duration-200 backdrop-blur-sm">
                                            <i class="fas fa-broom mr-1"></i>清空
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 实时日期时间显示区域 - 美化版 -->
                            <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-4 border-b border-green-100">
                                <div class="grid grid-cols-1 gap-3">
                                    <div class="flex items-center justify-between bg-white rounded-lg p-3 shadow-sm border border-green-100">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                                            <span class="text-green-700 font-medium text-sm">当前日期时间（秒）</span>
                                        </div>
                                        <span id="current-datetime-full" class="font-mono text-green-800 font-bold text-lg bg-green-50 px-3 py-1 rounded-md cursor-pointer hover:bg-green-100 transition-colors" title="点击复制"></span>
                                    </div>
                                    <div class="flex items-center justify-between bg-white rounded-lg p-3 shadow-sm border border-emerald-100">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                                            <span class="text-emerald-700 font-medium text-sm">当前日期时间（毫秒）</span>
                                        </div>
                                        <span id="current-datetime-milliseconds" class="font-mono text-emerald-800 font-bold text-lg bg-emerald-50 px-3 py-1 rounded-md cursor-pointer hover:bg-emerald-100 transition-colors" title="点击复制"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- 输入区域 -->
                            <div class="p-6">
                                <div class="space-y-4">
                                    <div>
                                        <label for="date-to-ts-input" class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                                            <i class="fas fa-edit mr-2 text-green-500"></i>输入日期时间
                                        </label>
                                        <input type="text" id="date-to-ts-input"
                                               class="w-full p-4 border-2 border-gray-200 rounded-xl font-mono text-lg focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-200 bg-gray-50 hover:bg-white"
                                               placeholder="输入日期时间...">
                                    </div>

                                    <!-- 转换按钮 -->
                                    <button id="date-to-ts-btn" class="w-full bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 hover:shadow-lg flex items-center justify-center space-x-2">
                                        <i class="fas fa-exchange-alt"></i>
                                        <span>转换</span>
                                        <div class="hidden" id="date-loading">
                                            <i class="fas fa-spinner fa-spin"></i>
                                        </div>
                                    </button>

                                    <div>
                                        <label class="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
                                            <i class="fas fa-hashtag mr-2 text-blue-500"></i>转换结果
                                        </label>
                                        <div id="date-to-ts-output" class="w-full p-4 bg-gradient-to-br from-gray-50 to-gray-100 border-2 border-gray-200 rounded-xl text-sm min-h-[120px] whitespace-pre-wrap font-mono overflow-auto">
                                            <div class="text-center py-8 text-gray-500">
                                                <i class="fas fa-info-circle text-2xl mb-2"></i>
                                                <div class="text-sm">支持格式：完整日期、年月日、年月等多种格式</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 格式支持说明 - 美化版 -->
                            <div class="bg-gradient-to-r from-green-50 to-emerald-50 p-4 border-t border-green-100">
                                <div class="flex items-center mb-2">
                                    <i class="fas fa-info-circle text-green-500 mr-2"></i>
                                    <span class="font-semibold text-green-700 text-sm">支持格式</span>
                                </div>
                                <div class="grid grid-cols-1 gap-2 text-xs">
                                    <div class="flex items-center space-x-2 bg-white rounded-lg p-2 border border-green-100">
                                        <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                        <span class="text-gray-700"><strong>完整:</strong> 2025-06-27 19:40:25</span>
                                    </div>
                                    <div class="flex items-center space-x-2 bg-white rounded-lg p-2 border border-green-100">
                                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                        <span class="text-gray-700"><strong>时分:</strong> 2025-06-27 19:40</span>
                                    </div>
                                    <div class="flex items-center space-x-2 bg-white rounded-lg p-2 border border-green-100">
                                        <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                        <span class="text-gray-700"><strong>仅日期:</strong> 2025-06-27</span>
                                    </div>
                                    <div class="flex items-center space-x-2 bg-white rounded-lg p-2 border border-green-100">
                                        <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                                        <span class="text-gray-700"><strong>年月:</strong> 2025-06</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 日期计算器工具 -->
            <div id="date-calculator-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">日期计算器工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: 日期差计算 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-blue-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="date-diff-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-calculator w-4 h-4 mr-1"></i>计算差值
                                </button>
                                <button id="clear-date-diff-btn" class="tool-btn hover:bg-blue-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-date-diff-example-btn" class="tool-btn text-blue-600 hover:bg-blue-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-download w-3 h-3 mr-1"></i>载入示例
                                </button>
                            </div>
                        </div>
                        <div class="bg-blue-50 px-3 py-1 border-l border-r text-xs text-blue-700 font-medium">
                            日期差计算:
                        </div>
                        <div class="flex-1 border-l border-r border-b bg-blue-50 rounded-b-lg p-3">
                            <div class="space-y-3">
                                <div>
                                    <label for="diff-start-date" class="block text-sm font-medium text-gray-600 mb-1">起始日期:</label>
                                    <input type="text" id="diff-start-date" class="w-full p-2 border border-gray-300 rounded-lg font-mono text-sm" value="2020-01-01 12:00:00">
                                </div>
                                <div>
                                    <label for="diff-end-date" class="block text-sm font-medium text-gray-600 mb-1">结束日期 (留空为当前):</label>
                                    <input type="text" id="diff-end-date" class="w-full p-2 border border-gray-300 rounded-lg font-mono text-sm" placeholder="YYYY-MM-DD HH:MM:SS">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">计算结果:</label>
                                    <div id="date-diff-output" class="w-full p-3 bg-white border rounded-lg text-sm min-h-[100px] whitespace-pre-wrap font-mono"></div>
                                </div>
                            </div>
                        </div>
                        <div id="date-diff-message" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>

                    <!-- 右侧: 日期偏移计算 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 右侧工具栏 -->
                        <div class="flex items-center justify-between bg-green-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="date-offset-btn" class="tool-btn bg-green-600 text-white hover:bg-green-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-plus w-4 h-4 mr-1"></i>计算日期
                                </button>
                                <button id="clear-date-offset-btn" class="tool-btn hover:bg-green-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="load-date-offset-example-btn" class="tool-btn text-green-600 hover:bg-green-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-download w-3 h-3 mr-1"></i>载入示例
                                </button>
                            </div>
                        </div>
                        <div class="bg-green-50 px-3 py-1 border-l border-r text-xs text-green-700 font-medium">
                            日期偏移计算:
                        </div>
                        <div class="flex-1 border-l border-r border-b bg-green-50 rounded-b-lg p-3">
                            <div class="space-y-3">
                                <div>
                                    <label for="offset-start-date" class="block text-sm font-medium text-gray-600 mb-1">起始日期:</label>
                                    <input type="text" id="offset-start-date" class="w-full p-2 border border-gray-300 rounded-lg font-mono text-sm">
                                </div>
                                <div>
                                    <label for="offset-days" class="block text-sm font-medium text-gray-600 mb-1">偏移天数 (负数为过去):</label>
                                    <input type="number" id="offset-days" class="w-full p-2 border border-gray-300 rounded-lg font-mono text-sm" value="195">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-600 mb-1">计算结果:</label>
                                    <div id="date-offset-output" class="w-full p-3 bg-white border rounded-lg text-sm min-h-[100px] whitespace-pre-wrap font-mono"></div>
                                </div>
                            </div>
                        </div>
                        <div id="date-offset-message" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>
            <!-- 文本对比工具 -->
            <div id="text-compare-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">文本对比工具</h2>

                <!-- 顶部工具栏 -->
                <div class="flex items-center justify-between mb-4 flex-shrink-0">
                    <!-- 左侧通用按钮 -->
                    <div class="flex items-center space-x-4">
                        <button id="toggle-line-numbers" class="flex items-center px-3 py-2 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors">
                            <i class="fas fa-list-ol mr-2"></i>显示行号
                        </button>
                        <button id="undo-btn" class="flex items-center px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors" disabled>
                            <i class="fas fa-undo mr-2"></i>撤销
                        </button>
                    </div>

                    <!-- 右侧示例按钮 -->
                    <div class="flex items-center space-x-2">
                        <button id="load-json-example" class="px-3 py-2 bg-purple-100 text-purple-700 rounded-lg hover:bg-purple-200 transition-colors text-sm">
                            Json数据示例
                        </button>
                        <button id="load-text-example" class="px-3 py-2 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors text-sm">
                            文本数据示例
                        </button>
                        <button id="load-config-example" class="px-3 py-2 bg-orange-100 text-orange-700 rounded-lg hover:bg-orange-200 transition-colors text-sm">
                            配置文件示例
                        </button>
                    </div>
                </div>

                <!-- 主要内容区域 -->
                <div class="flex-1 flex space-x-4 overflow-hidden">
                    <!-- 左侧: 文本A区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-blue-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <span class="font-medium">文本A</span>
                                <button id="clear-text-a-btn" class="tool-btn hover:bg-blue-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                                <button id="copy-text-a-btn" class="tool-btn hover:bg-blue-100 px-2 py-1 rounded">
                                    <i class="fas fa-copy w-4 h-4 mr-1"></i>复制
                                </button>
                            </div>
                        </div>

                        <!-- 文本A编辑区 -->
                        <div class="flex-1 flex border border-blue-200 rounded-b-lg overflow-hidden bg-white">
                            <!-- 行号区域 -->
                            <div id="text-a-line-numbers" class="bg-blue-50 border-r border-blue-200 text-xs text-gray-500 font-mono select-none min-w-[3rem] overflow-hidden">
                                <!-- 行号将通过JavaScript动态生成 -->
                            </div>
                            <!-- 文本输入区 -->
                            <div class="flex-1 relative">
                                <div id="text-a-editor" class="absolute inset-0 font-mono text-sm leading-6 p-3 overflow-auto whitespace-pre-wrap" contenteditable="true" spellcheck="false"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧: 文本B区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 右侧工具栏 -->
                        <div class="flex items-center justify-between bg-green-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <span class="font-medium">文本B</span>
                                <button id="clear-text-b-btn" class="tool-btn hover:bg-green-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                                <button id="copy-text-b-btn" class="tool-btn hover:bg-green-100 px-2 py-1 rounded">
                                    <i class="fas fa-copy w-4 h-4 mr-1"></i>复制
                                </button>
                            </div>
                        </div>

                        <!-- 文本B编辑区 -->
                        <div class="flex-1 flex border border-green-200 rounded-b-lg overflow-hidden bg-white">
                            <!-- 行号区域 -->
                            <div id="text-b-line-numbers" class="bg-green-50 border-r border-green-200 text-xs text-gray-500 font-mono select-none min-w-[3rem] overflow-hidden">
                                <!-- 行号将通过JavaScript动态生成 -->
                            </div>
                            <!-- 文本输入区 -->
                            <div class="flex-1 relative">
                                <div id="text-b-editor" class="absolute inset-0 font-mono text-sm leading-6 p-3 overflow-auto whitespace-pre-wrap" contenteditable="true" spellcheck="false"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="text-compare-message-area" class="mt-2 text-sm text-red-600 h-5 flex-shrink-0"></div>
            </div>





            <!-- HTML编码解码工具 -->
            <div id="html-entity-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">HTML编码解码工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: 原始文本区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-blue-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="html-entity-encode-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-arrow-down w-4 h-4 mr-1"></i>编码
                                </button>
                                <button id="clear-html-entity-input-btn" class="tool-btn hover:bg-blue-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="copy-html-entity-input-btn" class="tool-btn text-blue-600 hover:bg-blue-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-copy w-3 h-3 mr-1"></i>复制
                                </button>
                                <button id="load-html-entity-example-btn" class="tool-btn text-blue-600 hover:bg-blue-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-download w-3 h-3 mr-1"></i>载入示例
                                </button>
                            </div>
                        </div>
                        <div class="bg-blue-50 px-3 py-1 border-l border-r text-xs text-blue-700 font-medium">
                            原始文本 (Plain Text):
                        </div>
                        <textarea id="html-entity-input" class="flex-1 w-full p-3 border-l border-r border-b bg-blue-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="输入原始HTML文本，如: <div>中文</div>"></textarea>
                        <div id="html-entity-input-message" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>

                    <!-- 右侧: HTML实体区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 右侧工具栏 -->
                        <div class="flex items-center justify-between bg-green-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="html-entity-decode-btn" class="tool-btn bg-green-600 text-white hover:bg-green-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-arrow-up w-4 h-4 mr-1"></i>解码
                                </button>
                                <button id="clear-html-entity-output-btn" class="tool-btn hover:bg-green-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="copy-html-entity-output-btn" class="tool-btn text-green-600 hover:bg-green-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-copy w-3 h-3 mr-1"></i>复制
                                </button>
                                <button id="load-html-entity-encoded-example-btn" class="tool-btn text-green-600 hover:bg-green-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-download w-3 h-3 mr-1"></i>载入示例
                                </button>
                            </div>
                        </div>
                        <div class="bg-green-50 px-3 py-1 border-l border-r text-xs text-green-700 font-medium">
                            HTML实体 (HTML Entities):
                        </div>
                        <textarea id="html-entity-output" class="flex-1 w-full p-3 border-l border-r border-b bg-green-50 rounded-b-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-200 resize-none font-mono text-sm" placeholder="HTML实体编码结果将显示在这里..."></textarea>
                        <div id="html-entity-output-message" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>

            <!-- URL编码解码工具 -->
            <div id="url-encode-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">URL编码解码工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: 原始文本区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-blue-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="url-encode-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-arrow-down w-4 h-4 mr-1"></i>编码
                                </button>
                                <button id="clear-url-input-btn" class="tool-btn hover:bg-blue-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="copy-url-input-btn" class="tool-btn text-blue-600 hover:bg-blue-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-copy w-3 h-3 mr-1"></i>复制
                                </button>
                                <button id="load-url-example-btn" class="tool-btn text-blue-600 hover:bg-blue-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-download w-3 h-3 mr-1"></i>载入示例
                                </button>
                            </div>
                        </div>
                        <div class="bg-blue-50 px-3 py-1 border-l border-r text-xs text-blue-700 font-medium">
                            原始文本 (Plain Text):
                        </div>
                        <textarea id="url-input" class="flex-1 w-full p-3 border-l border-r border-b bg-blue-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="输入原始URL或文本，如: 中文测试"></textarea>
                        <div id="url-input-message" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>

                    <!-- 右侧: URL编码区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 右侧工具栏 -->
                        <div class="flex items-center justify-between bg-green-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="url-decode-btn" class="tool-btn bg-green-600 text-white hover:bg-green-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-arrow-up w-4 h-4 mr-1"></i>解码
                                </button>
                                <button id="clear-url-output-btn" class="tool-btn hover:bg-green-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="copy-url-output-btn" class="tool-btn text-green-600 hover:bg-green-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-copy w-3 h-3 mr-1"></i>复制
                                </button>
                                <button id="load-url-encoded-example-btn" class="tool-btn text-green-600 hover:bg-green-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-download w-3 h-3 mr-1"></i>载入示例
                                </button>
                            </div>
                        </div>
                        <div class="bg-green-50 px-3 py-1 border-l border-r text-xs text-green-700 font-medium">
                            URL编码 (URL Encoded):
                        </div>
                        <textarea id="url-output" class="flex-1 w-full p-3 border-l border-r border-b bg-green-50 rounded-b-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-200 resize-none font-mono text-sm" placeholder="URL编码结果将显示在这里..."></textarea>
                        <div id="url-output-message" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>

            <!-- Unicode编码解码工具 -->
            <div id="unicode-tool" class="tool-content hidden flex flex-col h-full bg-white p-6 rounded-xl shadow-lg">
                <h2 class="text-2xl font-semibold text-gray-700 border-b pb-4 mb-6 flex-shrink-0">Unicode编码解码工具</h2>
                <div class="flex-1 flex space-x-6 overflow-hidden">
                    <!-- 左侧: 原始文本区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 左侧工具栏 -->
                        <div class="flex items-center justify-between bg-blue-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="unicode-encode-btn" class="tool-btn bg-blue-600 text-white hover:bg-blue-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-arrow-down w-4 h-4 mr-1"></i>编码
                                </button>
                                <button id="clear-unicode-input-btn" class="tool-btn hover:bg-blue-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="copy-unicode-input-btn" class="tool-btn text-blue-600 hover:bg-blue-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-copy w-3 h-3 mr-1"></i>复制
                                </button>
                                <button id="load-unicode-example-btn" class="tool-btn text-blue-600 hover:bg-blue-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-download w-3 h-3 mr-1"></i>载入示例
                                </button>
                            </div>
                        </div>
                        <div class="bg-blue-50 px-3 py-1 border-l border-r text-xs text-blue-700 font-medium">
                            原始文本 (Plain Text):
                        </div>
                        <textarea id="unicode-input" class="flex-1 w-full p-3 border-l border-r border-b bg-blue-50 rounded-b-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200 resize-none font-mono text-sm" placeholder="输入原始文本，如: 中文 Hello 🌍"></textarea>
                        <div id="unicode-input-message" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>

                    <!-- 右侧: Unicode编码区 -->
                    <div class="w-1/2 flex flex-col">
                        <!-- 右侧工具栏 -->
                        <div class="flex items-center justify-between bg-green-50 p-2 rounded-t-lg border-b text-sm text-gray-600 flex-shrink-0">
                            <div class="flex items-center space-x-3">
                                <button id="unicode-decode-btn" class="tool-btn bg-green-600 text-white hover:bg-green-700 px-3 py-1 rounded font-medium transition-colors">
                                    <i class="fas fa-arrow-up w-4 h-4 mr-1"></i>解码
                                </button>
                                <button id="clear-unicode-output-btn" class="tool-btn hover:bg-green-100 px-2 py-1 rounded">
                                    <i class="fas fa-eraser w-4 h-4 mr-1"></i>清空
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button id="copy-unicode-output-btn" class="tool-btn text-green-600 hover:bg-green-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-copy w-3 h-3 mr-1"></i>复制
                                </button>
                                <button id="load-unicode-encoded-example-btn" class="tool-btn text-green-600 hover:bg-green-100 px-2 py-1 rounded text-xs">
                                    <i class="fas fa-download w-3 h-3 mr-1"></i>载入示例
                                </button>
                            </div>
                        </div>
                        <div class="bg-green-50 px-3 py-1 border-l border-r text-xs text-green-700 font-medium">
                            Unicode编码 (Unicode Encoded):
                        </div>
                        <textarea id="unicode-output" class="flex-1 w-full p-3 border-l border-r border-b bg-green-50 rounded-b-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition duration-200 resize-none font-mono text-sm" placeholder="Unicode编码结果将显示在这里..."></textarea>
                        <div id="unicode-output-message" class="mt-2 text-sm text-gray-500 h-5 flex-shrink-0"></div>
                    </div>
                </div>
            </div>

        </main>
    </div>

    <div id="copy-modal" class="hidden fixed top-5 right-5 bg-green-500 text-white py-2 px-4 rounded-lg shadow-lg opacity-0">复制成功!</div>

    <!-- 帮助模态框 -->
    <div id="help-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-xl shadow-2xl max-w-4xl max-h-[90vh] overflow-y-auto m-4">
            <div class="sticky top-0 bg-gradient-to-r from-blue-500 to-indigo-600 text-white p-6 rounded-t-xl">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-question-circle text-2xl"></i>
                        <div>
                            <h3 class="text-xl font-bold">文本对比工具使用指南</h3>
                            <p class="text-blue-100 text-sm">功能介绍与快捷键说明</p>
                        </div>
                    </div>
                    <button id="close-help-modal" class="text-white hover:text-blue-200 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>

            <div class="p-6 space-y-6">
                <!-- 功能介绍 -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-star text-yellow-500 mr-2"></i>主要功能
                    </h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <h5 class="font-medium text-blue-800 mb-2">智能对比</h5>
                            <p class="text-sm text-blue-600">支持逐行、逐字符对比，智能识别文本差异</p>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <h5 class="font-medium text-green-800 mb-2">双向分析</h5>
                            <p class="text-sm text-green-600">A→B 和 B→A 双向对比，清晰展示变化方向</p>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <h5 class="font-medium text-purple-800 mb-2">实时对比</h5>
                            <p class="text-sm text-purple-600">输入时自动对比，即时查看差异结果</p>
                        </div>
                        <div class="bg-orange-50 p-4 rounded-lg">
                            <h5 class="font-medium text-orange-800 mb-2">主题切换</h5>
                            <p class="text-sm text-orange-600">支持明暗主题，适应不同使用环境</p>
                        </div>
                    </div>
                </div>

                <!-- 快捷键 -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-keyboard text-indigo-500 mr-2"></i>快捷键
                    </h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">开始对比</span>
                                <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl + Enter</kbd>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">清空全部</span>
                                <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl + D</kbd>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">撤销</span>
                                <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl + Z</kbd>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">重做</span>
                                <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl + Y</kbd>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">搜索</span>
                                <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl + F</kbd>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">全屏模式</span>
                                <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">F11</kbd>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 使用技巧 -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>使用技巧
                    </h4>
                    <div class="space-y-3">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-blue-600 text-xs font-bold">1</span>
                            </div>
                            <div>
                                <p class="text-sm text-gray-700"><strong>拖拽调整：</strong>可以拖拽中间的分隔线调整左右面板大小</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-green-600 text-xs font-bold">2</span>
                            </div>
                            <div>
                                <p class="text-sm text-gray-700"><strong>侧边导航：</strong>右侧导航栏显示所有差异位置，点击快速跳转</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-purple-600 text-xs font-bold">3</span>
                            </div>
                            <div>
                                <p class="text-sm text-gray-700"><strong>搜索功能：</strong>在对比结果中搜索特定内容，支持正则表达式</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-orange-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <span class="text-orange-600 text-xs font-bold">4</span>
                            </div>
                            <div>
                                <p class="text-sm text-gray-700"><strong>对比选项：</strong>根据需要选择忽略空白字符、大小写等选项</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 示例数据 -->
                <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                        <i class="fas fa-code text-green-500 mr-2"></i>示例数据
                    </h4>
                    <div class="bg-gray-50 rounded-lg p-4">
                        <p class="text-sm text-gray-600 mb-3">点击"载入示例"按钮可以快速体验对比功能：</p>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-xs">
                            <div>
                                <h6 class="font-medium text-red-700 mb-2">文本 A (原始版本)</h6>
                                <pre class="bg-red-50 p-2 rounded text-red-600 overflow-x-auto">function hello() {
    console.log("Hello World");
    return true;
}</pre>
                            </div>
                            <div>
                                <h6 class="font-medium text-green-700 mb-2">文本 B (修改版本)</h6>
                                <pre class="bg-green-50 p-2 rounded text-green-600 overflow-x-auto">function hello(name) {
    console.log("Hello " + name);
    console.log("Welcome!");
    return true;
}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 外部自定义脚本 -->
    <script src="/static/js/main.js" defer></script>
</body>
</html>
