document.addEventListener('DOMContentLoaded', () => {
    // --- 示例数据 ---
    // 真实的浏览器Headers示例数据 - 来自X.com的实际请求
    const MOCK_HEADERS_DATA = `:authority
x.com
:method
POST
:path
/i/api/1.1/graphql/user_flow.json
:scheme
https
accept
*/*
accept-encoding
gzip, deflate, br, zstd
accept-language
zh-CN,zh;q=0.9,en;q=0.8
authorization
Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA
cache-control
no-cache
content-length
57116
content-type
application/x-www-form-urlencoded
cookie
night_mode=2; kdt=DvRre8Hb45E9zzhyckgCjhBemuPTHz9SobsO94oN; personalization_id="v1_VhLl4tH46YsQocCZ4/3ByA=="; ads_prefs="HBERAAA="; auth_multi="1542385816332554240:d3bf62ce55e222a2e3128c2e2bf0c0cada114e0c"; auth_token=810611c9eaae6ff70058ec4633b84205889b6db9; guest_id_ads=v1%3A174832089003353885; guest_id_marketing=v1%3A174832089003353885; guest_id=v1%3A174832089003353885; twid=u%3D1273879219764772864; ct0=508c32c2219787ac73a4de464e9ee67155b8bae5f2e0bb36d235be50542b24cf3a03105185032b659d74a147b3c267941de071ee19e2ed9701008cc65fba7463dbc6c2bee0bdbe742c3957ae73ccf7c3; _gcl_au=1.1.381970776.1748449252; _ga=GA1.1.17747486.1748449252; _ga_BT3WQMJWD7=GS2.1.s1748449251$o1$g0$t1748449260$j51$l0$h1003779670; external_referer=padhuUp37zjgzgv1mFWxJ12Ozwit7owX|0|8e8t2xd8A2w%3D; lang=zh-cn; __cf_bm=JIwaMOqQIjJPcW3dh5WdSSk2MKarwNSi__7TP_oXLpI-1751013357-1.0.1.1-eMw0MRTZFcV6VzM2XAMscLyXitaqMKsVZYXaVDGNbMxfyF8MeBd.FkGkmZtNvSqfjhHsyePGtprqLDhtZoEJDDX15FXnYqV5538jq5MOVdE
origin
https://x.com
pragma
no-cache
priority
u=1, i
referer
https://x.com/xifancry
sec-ch-ua
"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"
sec-ch-ua-mobile
?0
sec-ch-ua-platform
"Windows"
sec-fetch-dest
empty
sec-fetch-mode
cors
sec-fetch-site
same-origin
user-agent
Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36
x-client-transaction-id
jDnszg+V0xtiRW1AfeJpMcwLEoMtAbFDBDxY5FnOxgOm0TsLegzN0NWJmd7ZeG0C+Px5gohRLX9eIHo5l4pIQoeg6AJRjw
x-csrf-token
508c32c2219787ac73a4de464e9ee67155b8bae5f2e0bb36d235be50542b24cf3a03105185032b659d74a147b3c267941de071ee19e2ed9701008cc65fba7463dbc6c2bee0bdbe742c3957ae73ccf7c3
x-twitter-active-user
yes
x-twitter-auth-type
OAuth2Session
x-twitter-client-language
zh-cn
x-xp-forwarded-for
9ef4ed50b9c49045d2fa73ad1cf78887fc106aa6bea9d6842c35078208fe355b72ab1129544ee3c5d0415c30b2a29fb288e9b006f09bea9bb8f0e52e2fd9d958ea17088064399062f56b51df1b6684f71a685b570407843018ddf3d5eebd6d16b75bdce6ae4c1338d564f8a6952c59f1da8e6848ad865713a55a1e4271c9d7d57ac823ba887ea3de92dbdce16e0ac7b9d9bc8809300353698f96d5e3b4072bd3fb041504e334740d95079a0569d057a198e5c45f4be7266892e8114045c1ffa4507b9036006690e174b3c1c917ea7be4cbd21bd5c628f6547d4ef91d2753fcd4416ae1c1ac61bd393410251197ed5e28c5750a05e9c3d4372c9001`;

    const MOCK_JSON_DATA = `{
    "user_id": 12345, "username": "爬虫爱好者", "is_active": true,
    "roles": ["admin", "editor"],
    "profile": {"email": "<EMAIL>", "address": null, "preferences": {"theme": "dark", "notifications": {"email": true, "sms": false}}},
    "logs": [{"timestamp": 1672531200, "action": "login"}, {"timestamp": 1672534800, "action": "update_profile"}]
}`;

    const MOCK_SORTABLE_JSON_DATA = `{
        "zebra": 1, "apple": 2, "metadata": { "version": "1.0", "author": "tester", "timestamp": "2025-01-01" },
        "data": [ {"id": 10, "name": "Xylophone"}, {"id": 2, "name": "Tambourine"} ], "cat": 3
    }`;

    // 嵌套转义JSON示例数据 - 专门用于智能解析功能
    const MOCK_NESTED_JSON_DATA = `{
    "type": "baiinfo",
    "data": "[{\\"date\\": \\"2025-06-27\\", \\"product_name\\": \\"\\\\u6c2f\\\\u866b\\\\u82ef\\\\u7532\\\\u9170\\\\u80fa97%\\", \\"product_id\\": 1588348470396473940, \\"price\\": 282500.0, \\"unit\\": \\"\\\\u5143/\\\\u5428\\", \\"source\\": \\"detail\\"}]",
    "status": "success",
    "nested_info": "{\\"level\\": 2, \\"description\\": \\"\\\\u8fd9\\\\u662f\\\\u4e00\\\\u4e2a\\\\u5d4c\\\\u5957\\\\u7684JSON\\\\u793a\\\\u4f8b\\"}"
}`;

    const MOCK_XML_DATA = `<?xml version="1.0" encoding="UTF-8"?><bookstore><book category="cooking"><title lang="en">Everyday Italian</title><author>Giada De Laurentiis</author><year>2005</year><price>30.00</price></book><book category="children"><title lang="en">Harry Potter</title><author>J K. Rowling</author><year>2005</year><price>29.99</price></book></bookstore>`;

    const MOCK_HTML_DATA = `<!DOCTYPE html><html><head><title>一个示例</title><style>body { font-family: sans-serif; }</style></head><body><h1>标题</h1><p>这是一个段落, <b>部分加粗</b>。</p><ul><li>项目一</li><li>项目二</li></ul></body></html>`;

    const MOCK_RENDER_HTML_DATA = `<h1>这是一个一级标题</h1>
<p>这是一个段落，包含 <strong>加粗</strong> 和 <em>斜体</em> 文本。</p>
<p style="color: blue;">这是一个带有内联样式的蓝色段落。</p>
<ul>
    <li>列表项 1</li>
    <li>列表项 2</li>
</ul>
<p>下面是一个图片示例：</p>
<img src="https://news-bos.cdn.bcebos.com/mvideo/log-news.png" alt="Google Logo" width="136">
<hr>
<a href="https://news.baidu.com/"> 百度新闻</a>
<blockquote>这是一个引用块。</blockquote>`;

    const MOCK_URL_DATA = `https://example.com/path?param1=value1&param2=value2`;

    const MOCK_TEXT_A = `function hello() {
    console.log("Hello World");
    return true;
}

const user = {
    name: "张三",
    age: 25,
    city: "北京"
};

// 这是一个注释
let count = 0;`;

    const MOCK_TEXT_B = `function hello(name) {
    console.log("Hello " + name);
    console.log("Welcome!");
    return true;
}

const user = {
    name: "李四",
    age: 28,
    city: "上海",
    email: "<EMAIL>"
};

// 这是修改后的注释
let count = 10;
let isActive = true;`;

    // 编码解码工具示例数据
    const ENCODING_EXAMPLES = {


        // Base64编码示例
        base64: {
            original: `Hello 世界! 这是Base64编码测试。
包含中文、英文、特殊字符：©®™
爬虫工具箱 - Spider Tools`,
            encoded: `SGVsbG8g5LiW55WMISDovozkuKpCYXNlNjTnvJbnoIHmtYvor5XjgII=
5YyF5ZCr5Lit5paH44CB6Iux5paH44CB54m55q6K5a2X56ym77yM4pWp4pSu4oSi
54is6Jmr5bel5YW35LitIC0gU3BpZGVyIFRvb2xz`
        },

        // HTML实体编码示例
        htmlEntity: {
            original: `<div class="container">
    <h1>欢迎使用爬虫工具箱</h1>
    <p>这里有特殊字符：&、<、>、"、'</p>
    <a href="https://example.com?name=张三&age=25">链接示例</a>
</div>`,
            encoded: `&lt;div class=&quot;container&quot;&gt;
    &lt;h1&gt;欢迎使用爬虫工具箱&lt;/h1&gt;
    &lt;p&gt;这里有特殊字符：&amp;、&lt;、&gt;、&quot;、&#39;&lt;/p&gt;
    &lt;a href=&quot;https://example.com?name=张三&amp;age=25&quot;&gt;链接示例&lt;/a&gt;
&lt;/div&gt;`
        },

        // URL编码示例
        urlEncode: {
            original: `https://www.example.com/search?q=Python爬虫教程&category=编程技术&author=张三`,
            encoded: `https://www.example.com/search?q=Python%E7%88%AC%E8%99%AB%E6%95%99%E7%A8%8B&category=%E7%BC%96%E7%A8%8B%E6%8A%80%E6%9C%AF&author=%E5%BC%A0%E4%B8%89`
        },

        // Unicode编码示例
        unicode: {
            original: `Hello 世界! 🌍
这是Unicode编码测试
特殊符号：©®™€£¥`,
            encoded: `Hello \\u4e16\\u754c! \\ud83c\\udf0d
\\u8fd9\\u662fUnicode\\u7f16\\u7801\\u6d4b\\u8bd5
\\u7279\\u6b8a\\u7b26\\u53f7\\uff1a\\u00a9\\u00ae\\u2122\\u20ac\\u00a3\\u00a5`
        }
    };


    // --- 元素获取 ---
    const sidebarNav = document.getElementById('sidebar-nav');
    const toolContents = document.querySelectorAll('.tool-content');
    const copyModal = document.getElementById('copy-modal');

    // --- 页面路由/工具切换逻辑 ---
    if (sidebarNav) {
        sidebarNav.addEventListener('click', (e) => {
            e.preventDefault();
            const targetLink = e.target.closest('.sidebar-link');
            if (!targetLink) return;
            sidebarNav.querySelectorAll('.sidebar-link').forEach(link => link.classList.remove('active'));
            targetLink.classList.add('active');
            const toolId = targetLink.dataset.tool;
            toolContents.forEach(content => {
                content.id === toolId ? content.classList.remove('hidden') : content.classList.add('hidden');
            });
            // 当切换到时间转换工具时，执行其初始化
            if (toolId === 'timestamp-tool') {
                setTimeout(initTimestampTool, 100);
            } else if (toolId === 'date-calculator-tool') {
                setTimeout(initDateCalculatorTool, 100);
            }
        });
    }


    // --- Headers 工具逻辑 ---
    const convertHeadersBtn = document.getElementById('convert-headers-btn');
    const clearHeadersBtn = document.getElementById('clear-headers-btn');
    const rawHeadersInput = document.getElementById('raw-headers');
    const headersOutput = document.getElementById('headers-output');
    const cookiesOutput = document.getElementById('cookies-output');
    const headersMessageArea = document.getElementById('headers-message-area');
    const headersInputMessageArea = document.getElementById('headers-input-message-area');
    const loadExampleHeadersBtn = document.getElementById('load-example-headers');

    // Headers清空按钮功能
    if (clearHeadersBtn) {
        clearHeadersBtn.addEventListener('click', () => {
            if (rawHeadersInput) rawHeadersInput.value = '';
            if (headersOutput) {
                headersOutput.innerHTML = '';
                headersOutput.dataset.plainText = '';
            }
            if (cookiesOutput) {
                cookiesOutput.innerHTML = '';
                cookiesOutput.dataset.plainText = '';
            }
            if (headersMessageArea) headersMessageArea.textContent = '';
            if (headersInputMessageArea) headersInputMessageArea.textContent = '';
            if (headersInputMessageArea) headersInputMessageArea.textContent = '已清空所有内容';
            setTimeout(() => {
                if (headersInputMessageArea) headersInputMessageArea.textContent = '';
            }, 2000);
            console.log('Headers工具已清空');
        });
    }

    if (loadExampleHeadersBtn) {
        loadExampleHeadersBtn.addEventListener('click', () => {
            if (rawHeadersInput) rawHeadersInput.value = MOCK_HEADERS_DATA;
            if (headersInputMessageArea) headersInputMessageArea.textContent = '已载入示例数据';
            setTimeout(() => {
                if (headersInputMessageArea) headersInputMessageArea.textContent = '';
            }, 2000);
            if (convertHeadersBtn) convertHeadersBtn.click();
        });
    }
    if (convertHeadersBtn) {
        convertHeadersBtn.addEventListener('click', async () => {
            if (headersMessageArea) headersMessageArea.textContent = '';
            convertHeadersBtn.disabled = true;
            convertHeadersBtn.innerHTML = '<i class="fas fa-spinner fa-spin w-4 h-4 mr-1"></i>转换中...';
            try {
                const response = await fetch('/api/v1/convert-headers', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({raw_headers: rawHeadersInput.value})
                });
                if (!response.ok) throw new Error(`HTTP ${response.status}`);
                const result = await response.json();
                if (result.error) throw new Error(result.error);
                if (headersOutput) {
                    headersOutput.innerHTML = result.headers_html;
                    headersOutput.dataset.plainText = result.headers_plain;
                }
                if (cookiesOutput) {
                    cookiesOutput.innerHTML = result.cookies_html;
                    cookiesOutput.dataset.plainText = result.cookies_plain;
                }
            } catch (error) {
                if (headersMessageArea) headersMessageArea.textContent = `错误: ${error.message}`;
            } finally {
                convertHeadersBtn.disabled = false;
                convertHeadersBtn.innerHTML = '<i class="fas fa-magic w-4 h-4 mr-1"></i>转换格式';
            }
        });
    }
    const headersToolContainer = document.getElementById('headers-tool');
    if (headersToolContainer) {
        headersToolContainer.addEventListener('click', (e) => {
            const button = e.target.closest('[data-action]');
            if (button) {
                const action = button.dataset.action;
                switch (action) {
                    case 'copy-headers':
                        window.copyToClipboard('headers-output');
                        break;
                    case 'copy-cookies':
                        window.copyToClipboard('cookies-output');
                        break;
                }
            }
        });
    }

 // --- JSON 工具逻辑 ---
    const formatJsonBtn = document.getElementById('format-json-btn');
    const clearJsonBtn = document.getElementById('clear-json-btn');
    const rawJsonInput = document.getElementById('raw-json');
    const jsonOutput = document.getElementById('json-output');
    const jsonMessageArea = document.getElementById('json-message-area');
    const jsonInputMessageArea = document.getElementById('json-input-message-area');
    const loadExampleJsonBtn = document.getElementById('load-example-json');
    const loadSortableJsonBtn = document.getElementById('load-sortable-json');
    const loadNestedJsonBtn = document.getElementById('load-nested-json');
    const jsonToolbar = document.getElementById('json-toolbar');
    // const jsonSearchBar = document.getElementById('json-search-bar');
    // const jsonSearchInput = document.getElementById('json-search-input');
    // const jsonSearchClear = document.getElementById('json-search-clear');


    const jsonOptions = {
        sort_keys: false,
        compress: false,
        isEscaped: false,
        isCollapsed: false,
    };

    let originalJsonHTML = '';

    const handleFormatJson = async () => {
        if (!rawJsonInput.value.trim()) {
            jsonOutput.innerHTML = '';
            jsonOutput.dataset.plainText = '';
            return;
        }
        jsonMessageArea.textContent = '';
        formatJsonBtn.disabled = true;
        formatJsonBtn.innerHTML = '<i class="fas fa-spinner fa-spin w-4 h-4 mr-1"></i>处理中...';
        try {
            const response = await fetch('/api/v1/format-json', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    raw_json: rawJsonInput.value,
                    sort_keys: jsonOptions.sort_keys,
                    compress: jsonOptions.compress
                })
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);

            jsonOutput.innerHTML = result.json_html;
            jsonOutput.dataset.plainText = result.json_plain;
            originalJsonHTML = result.json_html;
        } catch (error) {
            jsonMessageArea.textContent = `错误: ${error.message}`;
            jsonOutput.innerHTML = '';
        } finally {
            formatJsonBtn.disabled = false;
            formatJsonBtn.innerHTML = '<i class="fas fa-magic w-4 h-4 mr-1"></i>格式化';
        }
    };

    formatJsonBtn.addEventListener('click', handleFormatJson);

    // 清空按钮功能
    if (clearJsonBtn) {
        clearJsonBtn.addEventListener('click', () => {
            rawJsonInput.value = '';
            jsonOutput.innerHTML = '';
            jsonOutput.dataset.plainText = '';
            jsonMessageArea.textContent = '';
            if (jsonInputMessageArea) jsonInputMessageArea.textContent = '';
            // 重置所有选项状态
            jsonOptions.sort_keys = false;
            jsonOptions.compress = false;
            jsonOptions.isEscaped = false;
            jsonOptions.isCollapsed = false;
            // 重置工具栏按钮状态
            jsonToolbar.querySelectorAll('.tool-btn.active').forEach(btn => btn.classList.remove('active'));
            if (jsonInputMessageArea) jsonInputMessageArea.textContent = '已清空所有内容';
            setTimeout(() => {
                if (jsonInputMessageArea) jsonInputMessageArea.textContent = '';
            }, 2000);
            console.log('JSON工具已清空');
        });
    }

    loadExampleJsonBtn.addEventListener('click', () => {
        rawJsonInput.value = MOCK_JSON_DATA;
        if (jsonInputMessageArea) jsonInputMessageArea.textContent = '已载入常规示例数据';
        setTimeout(() => {
            if (jsonInputMessageArea) jsonInputMessageArea.textContent = '';
        }, 2000);
        handleFormatJson();
    });
    loadSortableJsonBtn.addEventListener('click', () => {
        rawJsonInput.value = MOCK_SORTABLE_JSON_DATA;
        if (jsonInputMessageArea) jsonInputMessageArea.textContent = '已载入排序示例数据';
        setTimeout(() => {
            if (jsonInputMessageArea) jsonInputMessageArea.textContent = '';
        }, 2000);
        if (!jsonOptions.sort_keys) {
            jsonToolbar.querySelector('[data-action="sort"]').click();
        } else {
            handleFormatJson();
        }
    });

    loadNestedJsonBtn.addEventListener('click', () => {
        rawJsonInput.value = MOCK_NESTED_JSON_DATA;
        if (jsonInputMessageArea) {
            jsonInputMessageArea.textContent = '已载入嵌套转义示例数据，格式化按钮会自动处理嵌套JSON';
            jsonInputMessageArea.className = 'mt-2 text-sm text-purple-600 h-5 flex-shrink-0';
        }
        setTimeout(() => {
            if (jsonInputMessageArea) {
                jsonInputMessageArea.textContent = '';
                jsonInputMessageArea.className = 'mt-2 text-sm text-gray-500 h-5 flex-shrink-0';
            }
        }, 3000);
        // 自动格式化
        handleFormatJson();
    });

    jsonToolbar.addEventListener('click', (e) => {
        const button = e.target.closest('.tool-btn');
        if (!button) return;
        const action = button.dataset.action;

        switch (action) {
            case 'sort':
            case 'compress':
                jsonOptions[action] = !jsonOptions[action];
                button.classList.toggle('active');
                handleFormatJson();
                break;
            case 'escape':
                handleJsonEscape(button);
                break;
            case 'collapse':
                toggleAllJsonNodes();
                break;
           case 'search':
                toggleSearchBar('json'); // 传入工具前缀
                break;
            case 'copy':
                window.copyToClipboard('json-output');
                break;
        }
    });

    // JSON折叠/展开功能 - 新的交互式折叠
    if (jsonOutput) {
        jsonOutput.addEventListener('click', (e) => {
            const toggle = e.target.closest('.json-toggle');
            if (toggle) {
                const targetId = toggle.dataset.target;
                const content = document.getElementById(targetId);
                const icon = toggle.querySelector('.json-toggle-icon');

                if (content && icon) {
                    const isCollapsed = content.classList.contains('collapsed');

                    if (isCollapsed) {
                        // 展开
                        content.classList.remove('collapsed');
                        icon.classList.remove('fa-plus-square');
                        icon.classList.add('fa-minus-square');
                    } else {
                        // 折叠
                        content.classList.add('collapsed');
                        icon.classList.remove('fa-minus-square');
                        icon.classList.add('fa-plus-square');
                    }
                }
            }
        });
    }

    function handleJsonEscape(button) {
        const currentText = jsonOutput.dataset.plainText;
        if (!currentText) return;
        jsonOptions.isEscaped = !jsonOptions.isEscaped;
        button.classList.toggle('active');
        if (jsonOptions.isEscaped) {
            // 先将JSON压缩为一行，然后转义
            try {
                const parsedJson = JSON.parse(currentText);
                const compactJson = JSON.stringify(parsedJson, null, 0); // 压缩为一行
                const escapedString = JSON.stringify(compactJson);
                jsonOutput.innerHTML = `<span class="text-red-700">${escapedString}</span>`;
                jsonOutput.dataset.plainText = escapedString;
            } catch (e) {
                // 如果解析失败，直接转义原文本
                const escapedString = JSON.stringify(currentText);
                jsonOutput.innerHTML = `<span class="text-red-700">${escapedString}</span>`;
                jsonOutput.dataset.plainText = escapedString;
            }
        } else {
            handleFormatJson();
        }
    }

    function toggleAllJsonNodes() {
        jsonOptions.isCollapsed = !jsonOptions.isCollapsed;

        // 新的折叠系统：查找所有json-toggle元素
        const toggles = jsonOutput.querySelectorAll('.json-toggle');
        toggles.forEach(toggle => {
            const targetId = toggle.dataset.target;
            const content = document.getElementById(targetId);
            const icon = toggle.querySelector('.json-toggle-icon');

            if (content && icon) {
                if (jsonOptions.isCollapsed) {
                    // 全部折叠
                    content.classList.add('collapsed');
                    icon.classList.remove('fa-minus-square');
                    icon.classList.add('fa-plus-square');
                } else {
                    // 全部展开
                    content.classList.remove('collapsed');
                    icon.classList.remove('fa-plus-square');
                    icon.classList.add('fa-minus-square');
                }
            }
        });
    }

    // --- XML 工具逻辑 ---
    const formatXmlBtn = document.getElementById('format-xml-btn');
    const clearXmlBtn = document.getElementById('clear-xml-btn');
    const rawXmlInput = document.getElementById('raw-xml');
    const xmlOutput = document.getElementById('xml-output');
    const xmlMessageArea = document.getElementById('xml-message-area');
    const xmlInputMessageArea = document.getElementById('xml-input-message-area');
    const loadExampleXmlBtn = document.getElementById('load-example-xml');
    const xmlToolbar = document.getElementById('xml-toolbar');
    const xmlOptions = {compress: false};
    let originalXmlHTML = '';

    const handleFormatXml = async () => {
        if (!rawXmlInput || !rawXmlInput.value.trim()) {
            if (xmlOutput) {
                xmlOutput.innerHTML = '';
                xmlOutput.dataset.plainText = '';
            }
            return;
        }
        if (xmlMessageArea) xmlMessageArea.textContent = '';
        if (formatXmlBtn) {
            formatXmlBtn.disabled = true;
            formatXmlBtn.innerHTML = '<i class="fas fa-spinner fa-spin w-4 h-4 mr-1"></i>处理中...';
        }
        try {
            const response = await fetch('/api/v1/format-xml', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({raw_xml: rawXmlInput.value, compress: xmlOptions.compress})
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);
            if (xmlOutput) {
                xmlOutput.innerHTML = result.xml_html;
                xmlOutput.dataset.plainText = result.xml_plain;
                originalXmlHTML = result.xml_html;
            }
        } catch (error) {
            if (xmlMessageArea) xmlMessageArea.textContent = `错误: ${error.message}`;
            if (xmlOutput) xmlOutput.innerHTML = '';
        } finally {
            if (formatXmlBtn) {
                formatXmlBtn.disabled = false;
                formatXmlBtn.innerHTML = '<i class="fas fa-magic w-4 h-4 mr-1"></i>格式化';
            }
        }
    };

    if (formatXmlBtn) formatXmlBtn.addEventListener('click', handleFormatXml);

    // XML清空按钮功能
    if (clearXmlBtn) {
        clearXmlBtn.addEventListener('click', () => {
            if (rawXmlInput) rawXmlInput.value = '';
            if (xmlOutput) {
                xmlOutput.innerHTML = '';
                xmlOutput.dataset.plainText = '';
            }
            if (xmlMessageArea) xmlMessageArea.textContent = '';
            if (xmlInputMessageArea) xmlInputMessageArea.textContent = '';
            // 重置XML选项状态
            xmlOptions.compress = false;
            // 重置工具栏按钮状态
            if (xmlToolbar) xmlToolbar.querySelectorAll('.tool-btn.active').forEach(btn => btn.classList.remove('active'));
            if (xmlInputMessageArea) xmlInputMessageArea.textContent = '已清空所有内容';
            setTimeout(() => {
                if (xmlInputMessageArea) xmlInputMessageArea.textContent = '';
            }, 2000);
            console.log('XML工具已清空');
        });
    }

    if (loadExampleXmlBtn) loadExampleXmlBtn.addEventListener('click', () => {
        if (rawXmlInput) rawXmlInput.value = MOCK_XML_DATA;
        if (xmlInputMessageArea) xmlInputMessageArea.textContent = '已载入示例数据';
        setTimeout(() => {
            if (xmlInputMessageArea) xmlInputMessageArea.textContent = '';
        }, 2000);
        handleFormatXml();
    });
    if (xmlToolbar) xmlToolbar.addEventListener('click', (e) => {
        const button = e.target.closest('.tool-btn');
        if (!button) return;
        const action = button.dataset.action;
        switch (action) {
            case 'compress':
                xmlOptions.compress = !xmlOptions.compress;
                button.classList.toggle('active');
                handleFormatXml();
                break;
          // (新添加) START: XML搜索逻辑
            case 'search':
                toggleSearchBar('xml'); // 传入工具前缀
                break;
            case 'copy':
                window.copyToClipboard('xml-output');
                break;
            default:
                break;
        }
    });

    // --- HTML 格式化工具逻辑 ---
    const htmlFormatButton = document.getElementById('format-html-button');
    const clearHtmlBtn = document.getElementById('clear-html-btn');
    const htmlRawInput = document.getElementById('raw-html-input');
    const htmlOutputArea = document.getElementById('html-output');
    const htmlMsgArea = document.getElementById('html-message-area');
    const htmlInputMessageArea = document.getElementById('html-input-message-area');
    const htmlLoadExampleBtn = document.getElementById('load-example-html-btn');
    const htmlToolBar = document.getElementById('html-toolbar');

    const handleFormatHtmlAction = async () => {
        if (!htmlRawInput || !htmlRawInput.value.trim()) {
            if (htmlOutputArea) {
                htmlOutputArea.innerHTML = '';
                htmlOutputArea.dataset.plainText = '';
            }
            return;
        }
        if (htmlMsgArea) htmlMsgArea.textContent = '';
        if (htmlFormatButton) {
            htmlFormatButton.disabled = true;
            htmlFormatButton.innerHTML = '<i class="fas fa-spinner fa-spin w-4 h-4 mr-1"></i>处理中...';
        }
        try {
            const response = await fetch('/api/v1/format-html', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({raw_html: htmlRawInput.value})
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);
            if (htmlOutputArea) {
                htmlOutputArea.innerHTML = result.html_output;
                htmlOutputArea.dataset.plainText = result.plain_output;
            }
        } catch (error) {
            if (htmlMsgArea) htmlMsgArea.textContent = `错误: ${error.message}`;
            if (htmlOutputArea) htmlOutputArea.innerHTML = '';
        } finally {
            if (htmlFormatButton) {
                htmlFormatButton.disabled = false;
                htmlFormatButton.innerHTML = '<i class="fas fa-magic w-4 h-4 mr-1"></i>格式化';
            }
        }
    };

    if (htmlFormatButton) htmlFormatButton.addEventListener('click', handleFormatHtmlAction);

    // HTML清空按钮功能
    if (clearHtmlBtn) {
        clearHtmlBtn.addEventListener('click', () => {
            if (htmlRawInput) htmlRawInput.value = '';
            if (htmlOutputArea) {
                htmlOutputArea.innerHTML = '';
                htmlOutputArea.dataset.plainText = '';
            }
            if (htmlMsgArea) htmlMsgArea.textContent = '';
            if (htmlInputMessageArea) htmlInputMessageArea.textContent = '';
            if (htmlInputMessageArea) htmlInputMessageArea.textContent = '已清空所有内容';
            setTimeout(() => {
                if (htmlInputMessageArea) htmlInputMessageArea.textContent = '';
            }, 2000);
            console.log('HTML工具已清空');
        });
    }

    if (htmlLoadExampleBtn) {
        htmlLoadExampleBtn.addEventListener('click', () => {
            if (htmlRawInput) htmlRawInput.value = MOCK_HTML_DATA;
            if (htmlInputMessageArea) htmlInputMessageArea.textContent = '已载入示例数据';
            setTimeout(() => {
                if (htmlInputMessageArea) htmlInputMessageArea.textContent = '';
            }, 2000);
            handleFormatHtmlAction();
        });
    }
    if (htmlToolBar) {
        htmlToolBar.addEventListener('click', (e) => {
            const button = e.target.closest('.tool-btn');
            if (!button) return;
            const action = button.dataset.action;
            // 使用 switch 或多个 if 判断
            switch (action) {
                case 'copy':
                    window.copyToClipboard('html-output');
                    break;
                case 'search':
                    toggleSearchBar('html-format'); // 传入正确的工具前缀
                    break;
            }
        });
    }

    // --- HTML 渲染工具逻辑 ---
    const renderHtmlBtn = document.getElementById('render-html-btn');
    const clearHtmlRenderBtn = document.getElementById('clear-html-render-btn');
    const refreshRenderBtn = document.getElementById('refresh-render-btn');
    const htmlRenderInput = document.getElementById('html-render-input');
    const htmlRenderOutputIframe = document.getElementById('html-render-output');
    const htmlRenderInputMessageArea = document.getElementById('html-render-input-message-area');
    const htmlRenderMessageArea = document.getElementById('html-render-message-area');
    const loadExampleRenderBtn = document.getElementById('load-example-render-btn');

    const handleHtmlRender = () => {
        if (!htmlRenderInput || !htmlRenderOutputIframe) {
            console.error("HTML渲染工具的必要元素未找到。");
            return;
        }
        const rawHtml = htmlRenderInput.value;
        if (!rawHtml.trim()) {
            htmlRenderOutputIframe.srcdoc = '';
            if (htmlRenderMessageArea) htmlRenderMessageArea.textContent = '请输入HTML代码';
            return;
        }
        htmlRenderOutputIframe.srcdoc = rawHtml;
        if (htmlRenderMessageArea) htmlRenderMessageArea.textContent = '';
    };

    if (renderHtmlBtn) renderHtmlBtn.addEventListener('click', handleHtmlRender);

    // HTML渲染清空按钮功能
    if (clearHtmlRenderBtn) {
        clearHtmlRenderBtn.addEventListener('click', () => {
            if (htmlRenderInput) htmlRenderInput.value = '';
            if (htmlRenderOutputIframe) htmlRenderOutputIframe.srcdoc = '';
            if (htmlRenderMessageArea) htmlRenderMessageArea.textContent = '';
            if (htmlRenderInputMessageArea) htmlRenderInputMessageArea.textContent = '已清空所有内容';
            setTimeout(() => {
                if (htmlRenderInputMessageArea) htmlRenderInputMessageArea.textContent = '';
            }, 2000);
            console.log('HTML渲染工具已清空');
        });
    }

    // 刷新渲染按钮功能
    if (refreshRenderBtn) {
        refreshRenderBtn.addEventListener('click', () => {
            handleHtmlRender();
            if (htmlRenderInputMessageArea) htmlRenderInputMessageArea.textContent = '已刷新渲染';
            setTimeout(() => {
                if (htmlRenderInputMessageArea) htmlRenderInputMessageArea.textContent = '';
            }, 2000);
        });
    }

    if (loadExampleRenderBtn) {
        loadExampleRenderBtn.addEventListener('click', () => {
            if (htmlRenderInput) {
                htmlRenderInput.value = MOCK_RENDER_HTML_DATA;
                if (htmlRenderInputMessageArea) htmlRenderInputMessageArea.textContent = '已载入示例数据';
                setTimeout(() => {
                    if (htmlRenderInputMessageArea) htmlRenderInputMessageArea.textContent = '';
                }, 2000);
                handleHtmlRender();
            }
        });
    }

    // --- URL参数提取工具逻辑 ---
    const parseUrlBtn = document.getElementById('parse-url-btn');
    const clearUrlBtn = document.getElementById('clear-url-btn');
    const rawUrlInput = document.getElementById('raw-url-input');
    const baseUrlOutput = document.getElementById('base-url-output');
    const paramsOutput = document.getElementById('params-output');
    const urlMessageArea = document.getElementById('url-message-area');
    const urlInputMessageArea = document.getElementById('url-input-message-area');
    const loadExampleUrlBtn = document.getElementById('load-example-url-btn');
    const urlToolContainer = document.getElementById('url-tool');

    const handleParseUrl = async () => {
        if (!rawUrlInput || !rawUrlInput.value.trim()) return;
        if (urlMessageArea) urlMessageArea.textContent = '';
        if (parseUrlBtn) {
            parseUrlBtn.disabled = true;
            parseUrlBtn.innerHTML = '<i class="fas fa-spinner fa-spin w-4 h-4 mr-1"></i>提取中...';
        }
        try {
            const response = await fetch('/api/v1/parse-url', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({raw_url: rawUrlInput.value})
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);
            if (baseUrlOutput) {
                baseUrlOutput.innerHTML = result.base_url_html;
                baseUrlOutput.dataset.plainText = result.base_url_plain;
            }
            if (paramsOutput) {
                paramsOutput.innerHTML = result.params_html;
                paramsOutput.dataset.plainText = result.params_plain;
            }
        } catch (error) {
            if (urlMessageArea) urlMessageArea.textContent = `错误: ${error.message}`;
        } finally {
            if (parseUrlBtn) {
                parseUrlBtn.disabled = false;
                parseUrlBtn.innerHTML = '<i class="fas fa-magic w-4 h-4 mr-1"></i>提取参数';
            }
        }
    };

    if (parseUrlBtn) parseUrlBtn.addEventListener('click', handleParseUrl);

    // URL清空按钮功能
    if (clearUrlBtn) {
        clearUrlBtn.addEventListener('click', () => {
            if (rawUrlInput) rawUrlInput.value = '';
            if (baseUrlOutput) {
                baseUrlOutput.innerHTML = '';
                baseUrlOutput.dataset.plainText = '';
            }
            if (paramsOutput) {
                paramsOutput.innerHTML = '';
                paramsOutput.dataset.plainText = '';
            }
            if (urlMessageArea) urlMessageArea.textContent = '';
            if (urlInputMessageArea) urlInputMessageArea.textContent = '';
            if (urlInputMessageArea) urlInputMessageArea.textContent = '已清空所有内容';
            setTimeout(() => {
                if (urlInputMessageArea) urlInputMessageArea.textContent = '';
            }, 2000);
            console.log('URL工具已清空');
        });
    }

    if (loadExampleUrlBtn) {
        loadExampleUrlBtn.addEventListener('click', () => {
            if (rawUrlInput) rawUrlInput.value = MOCK_URL_DATA;
            if (urlInputMessageArea) urlInputMessageArea.textContent = '已载入示例数据';
            setTimeout(() => {
                if (urlInputMessageArea) urlInputMessageArea.textContent = '';
            }, 2000);
            handleParseUrl();
        });
    }
    if (urlToolContainer) {
        urlToolContainer.addEventListener('click', (e) => {
            const button = e.target.closest('[data-action]');
            if (button) {
                const action = button.dataset.action;
                switch (action) {
                    case 'copy-base-url':
                        window.copyToClipboard('base-url-output');
                        break;
                    case 'copy-params':
                        window.copyToClipboard('params-output');
                        break;
                }
            }
        });
    }

    // --- Base64 编码解码工具逻辑 ---
    const base64EncodeBtn = document.getElementById('base64-encode-btn');
    const base64DecodeBtn = document.getElementById('base64-decode-btn');
    const clearBase64PlainBtn = document.getElementById('clear-base64-plain-btn');
    const clearBase64EncodedBtn = document.getElementById('clear-base64-encoded-btn');
    const copyBase64PlainBtn = document.getElementById('copy-base64-plain-btn');
    const copyBase64EncodedBtn = document.getElementById('copy-base64-encoded-btn');
    const base64PlainInput = document.getElementById('base64-plain-input');
    const base64B64Input = document.getElementById('base64-b64-input');
    const base64MessageArea = document.getElementById('base64-message-area');
    const base64PlainMessageArea = document.getElementById('base64-plain-message-area');
    const base64EncodedMessageArea = document.getElementById('base64-encoded-message-area');
    const base64ToolContainer = document.getElementById('base64-tool');

    async function handleBase64Action(action) {
        let endpoint, payload, inputElement, outputElement;
        if (action === 'encode') {
            endpoint = '/api/v1/base64/encode';
            inputElement = base64PlainInput;
            outputElement = base64B64Input;
            payload = {plain_text: inputElement.value};
        } else {
            endpoint = '/api/v1/base64/decode';
            inputElement = base64B64Input;
            outputElement = base64PlainInput;
            payload = {b64_string: inputElement.value};
        }
        if (!inputElement || !inputElement.value.trim()) return;
        if (base64MessageArea) base64MessageArea.textContent = '';
        const btn = (action === 'encode') ? base64EncodeBtn : base64DecodeBtn;
        if (btn) {
            btn.disabled = true;
            btn.classList.add('opacity-50');
        }
        try {
            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(payload)
            });
            if (!response.ok) throw new Error(`HTTP ${response.status}`);
            const result = await response.json();
            if (result.error) throw new Error(result.error);
            if (outputElement) outputElement.value = result.result;
        } catch (error) {
            if (base64MessageArea) base64MessageArea.textContent = `错误: ${error.message}`;
        } finally {
            if (btn) {
                btn.disabled = false;
                btn.classList.remove('opacity-50');
            }
        }
    }

    if (base64EncodeBtn) base64EncodeBtn.addEventListener('click', () => handleBase64Action('encode'));
    if (base64DecodeBtn) base64DecodeBtn.addEventListener('click', () => handleBase64Action('decode'));

    // Base64明文清空按钮功能
    if (clearBase64PlainBtn) {
        clearBase64PlainBtn.addEventListener('click', () => {
            if (base64PlainInput) base64PlainInput.value = '';
            if (base64PlainMessageArea) base64PlainMessageArea.textContent = '已清空明文';
            setTimeout(() => {
                if (base64PlainMessageArea) base64PlainMessageArea.textContent = '';
            }, 2000);
        });
    }

    // Base64编码清空按钮功能
    if (clearBase64EncodedBtn) {
        clearBase64EncodedBtn.addEventListener('click', () => {
            if (base64B64Input) base64B64Input.value = '';
            if (base64EncodedMessageArea) base64EncodedMessageArea.textContent = '已清空Base64';
            setTimeout(() => {
                if (base64EncodedMessageArea) base64EncodedMessageArea.textContent = '';
            }, 2000);
        });
    }

    // Base64明文复制按钮功能
    if (copyBase64PlainBtn) {
        copyBase64PlainBtn.addEventListener('click', () => {
            if (base64PlainInput && base64PlainInput.value) {
                navigator.clipboard.writeText(base64PlainInput.value)
                    .then(() => window.showCopyModal())
                    .catch(err => console.error('复制失败:', err));
            }
        });
    }

    // Base64编码复制按钮功能
    if (copyBase64EncodedBtn) {
        copyBase64EncodedBtn.addEventListener('click', () => {
            if (base64B64Input && base64B64Input.value) {
                navigator.clipboard.writeText(base64B64Input.value)
                    .then(() => window.showCopyModal())
                    .catch(err => console.error('复制失败:', err));
            }
        });
    }

    // Base64载入示例按钮功能
    const loadBase64ExampleBtn = document.getElementById('load-base64-example-btn');
    const loadBase64EncodedExampleBtn = document.getElementById('load-base64-encoded-example-btn');

    if (loadBase64ExampleBtn) {
        loadBase64ExampleBtn.addEventListener('click', () => {
            if (base64PlainInput) {
                base64PlainInput.value = ENCODING_EXAMPLES.base64.original;
                if (base64PlainMessageArea) {
                    base64PlainMessageArea.textContent = '已载入原始示例';
                    base64PlainMessageArea.className = 'mt-2 text-sm text-green-600 h-5 flex-shrink-0';
                    setTimeout(() => {
                        if (base64PlainMessageArea) {
                            base64PlainMessageArea.textContent = '';
                            base64PlainMessageArea.className = 'mt-2 text-sm text-gray-500 h-5 flex-shrink-0';
                        }
                    }, 2000);
                }
            }
        });
    }

    if (loadBase64EncodedExampleBtn) {
        loadBase64EncodedExampleBtn.addEventListener('click', () => {
            if (base64B64Input) {
                base64B64Input.value = ENCODING_EXAMPLES.base64.encoded;
                if (base64EncodedMessageArea) {
                    base64EncodedMessageArea.textContent = '已载入编码示例';
                    base64EncodedMessageArea.className = 'mt-2 text-sm text-green-600 h-5 flex-shrink-0';
                    setTimeout(() => {
                        if (base64EncodedMessageArea) {
                            base64EncodedMessageArea.textContent = '';
                            base64EncodedMessageArea.className = 'mt-2 text-sm text-gray-500 h-5 flex-shrink-0';
                        }
                    }, 2000);
                }
            }
        });
    }

    // 旧的datetime工具已被删除，使用新的分离工具

    // --- 通用函数 ---
    window.showCopyModal = function () {
        if (!copyModal) return;
        copyModal.classList.remove('hidden', 'opacity-0');
        setTimeout(() => {
            copyModal.classList.add('opacity-0');
            setTimeout(() => copyModal.classList.add('hidden'), 300);
        }, 1500);
    };

    window.copyToClipboard = function (elementId) {
        const element = document.getElementById(elementId);
        if (!element) {
            console.error(`复制失败: 未找到元素 #${elementId}`);
            return;
        }
        const textToCopy = element.dataset.plainText;
        if (textToCopy === undefined || textToCopy === null) {
            // 对于textarea或input，如果data-plain-text不存在，则尝试复制其value
            if (element.value !== undefined) {
                navigator.clipboard.writeText(element.value).then(window.showCopyModal).catch(err => {
                    console.error('复制失败: ', err);
                    alert('复制失败!');
                });
            } else {
                console.error(`复制失败: 元素 #${elementId} 没有 "data-plain-text" 属性或 "value"。`);
            }
            return;
        }
        navigator.clipboard.writeText(textToCopy).then(window.showCopyModal).catch(err => {
            console.error('复制失败: ', err);
            alert('复制失败!');
        });
    };

// --- 【全新】通用搜索功能模块 ---

    // 存储每个工具的原始HTML和搜索状态
    const searchState = {
        json: { originalHTML: '', matches: [], currentIndex: -1, term: '' },
        xml: { originalHTML: '', matches: [], currentIndex: -1, term: '' },
        'html-format': { originalHTML: '', matches: [], currentIndex: -1, term: '' },
    };

    /**
     * 切换搜索栏的显示和隐藏
     * @param {string} toolPrefix - 'json', 'xml', or 'html-format'
     */
    function toggleSearchBar(toolPrefix) {
        const toolContainer = document.getElementById(`${toolPrefix}-tool`);
        if (!toolContainer) {
            console.error(`工具容器 ${toolPrefix}-tool 未找到`);
            return;
        }

        const searchBar = toolContainer.querySelector('.search-bar-container');
        const searchInput = toolContainer.querySelector('.search-input');
        // 处理输出元素ID的映射
        const outputId = toolPrefix === 'html-format' ? 'html-output' : `${toolPrefix}-output`;
        const outputEl = document.getElementById(outputId);

        if (!searchBar || !searchInput || !outputEl) {
            console.error(`搜索相关元素未找到: searchBar=${!!searchBar}, searchInput=${!!searchInput}, outputEl=${!!outputEl}`);
            return;
        }

        console.log(`搜索栏当前状态: hidden=${searchBar.classList.contains('hidden')}`);

        if (searchBar.classList.contains('hidden')) {
            // 显示搜索栏
            searchBar.classList.remove('hidden');
            searchBar.classList.add('flex');

            // 存储原始 HTML（只在第一次或内容变化时存储）
            if (!searchState[toolPrefix].originalHTML || searchState[toolPrefix].originalHTML !== outputEl.innerHTML) {
                searchState[toolPrefix].originalHTML = outputEl.innerHTML;
                console.log(`已存储 ${toolPrefix} 的原始HTML，长度: ${searchState[toolPrefix].originalHTML.length}`);
            }

            // 确保事件监听器只绑定一次
            if (!searchBar.dataset.initialized) {
                initializeSearchBarEvents(toolPrefix, toolContainer);
                searchBar.dataset.initialized = 'true';
                console.log(`已初始化 ${toolPrefix} 搜索栏事件`);
            }

            // 延迟设置焦点，确保元素已显示
            setTimeout(() => {
                searchInput.focus();
                console.log(`已设置 ${toolPrefix} 搜索框焦点`);
            }, 100);

        } else {
            // 隐藏搜索栏
            searchBar.classList.add('hidden');
            searchBar.classList.remove('flex');

            // 恢复原始 HTML
            if (searchState[toolPrefix].originalHTML) {
                outputEl.innerHTML = searchState[toolPrefix].originalHTML;
                console.log(`已恢复 ${toolPrefix} 的原始HTML`);
            }

            // 清理搜索状态
            searchState[toolPrefix].matches = [];
            searchState[toolPrefix].currentIndex = -1;
            searchState[toolPrefix].term = '';
            searchInput.value = '';

            // 更新搜索UI
            updateSearchUI(toolPrefix);
        }
    }

    /**
     * 为搜索栏绑定事件
     */
    function initializeSearchBarEvents(toolPrefix, container) {
        const searchInput = container.querySelector('.search-input');
        const prevBtn = container.querySelector('.search-prev-btn');
        const nextBtn = container.querySelector('.search-next-btn');
        const closeBtn = container.querySelector('.search-close-btn');

        searchInput.addEventListener('input', () => {
            performSearch(toolPrefix, searchInput.value);
        });

        prevBtn.addEventListener('click', () => navigateToMatch(toolPrefix, 'prev'));
        nextBtn.addEventListener('click', () => navigateToMatch(toolPrefix, 'next'));
        closeBtn.addEventListener('click', () => toggleSearchBar(toolPrefix));
    }

    /**
     * 执行搜索操作
     */
    function performSearch(toolPrefix, term) {
        const state = searchState[toolPrefix];
        state.term = term;

        // 处理输出元素ID的映射
        const outputId = toolPrefix === 'html-format' ? 'html-output' : `${toolPrefix}-output`;
        const outputEl = document.getElementById(outputId);

        // 确保有原始HTML可以恢复
        if (!state.originalHTML) {
            console.warn(`${toolPrefix} 没有原始HTML，无法执行搜索`);
            return;
        }

        // 恢复到干净状态再搜索
        outputEl.innerHTML = state.originalHTML;
        console.log(`执行搜索: "${term}", 原始HTML长度: ${state.originalHTML.length}`);

        if (!term.trim()) {
            state.matches = [];
            state.currentIndex = -1;
            updateSearchUI(toolPrefix);
            console.log(`搜索词为空，已清理搜索状态`);
            return;
        }

        // 执行搜索和高亮
        state.matches = highlightTextInNode(outputEl, term);
        state.currentIndex = state.matches.length > 0 ? 0 : -1;

        console.log(`搜索完成: 找到 ${state.matches.length} 个匹配项`);

        updateSearchUI(toolPrefix);
        if (state.matches.length > 0) {
            navigateToMatch(toolPrefix, 'first');
        }
    }

    /**
     * 在节点中高亮文本并返回匹配的元素数组
     */
    function highlightTextInNode(node, searchTerm) {
        if (!node || !searchTerm) {
            console.log('搜索参数无效:', { node: !!node, searchTerm });
            return [];
        }

        const regex = new RegExp(searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
        const walker = document.createTreeWalker(node, NodeFilter.SHOW_TEXT, null, false);
        let textNode;
        const matches = [];
        let processedNodes = 0;

        // 收集所有文本节点，避免在遍历时修改DOM
        const textNodes = [];
        while (textNode = walker.nextNode()) {
            if (textNode.parentElement.tagName === 'STYLE' || textNode.parentElement.tagName === 'SCRIPT') continue;
            textNodes.push(textNode);
        }

        console.log(`找到 ${textNodes.length} 个文本节点待处理`);

        // 处理每个文本节点
        textNodes.forEach(textNode => {
            const text = textNode.textContent;
            if (!text || !regex.test(text)) return;

            // 重置正则表达式的lastIndex
            regex.lastIndex = 0;

            let match;
            let lastIndex = 0;
            const fragment = document.createDocumentFragment();
            let nodeMatches = 0;

            while ((match = regex.exec(text)) !== null) {
                // 添加匹配前的文本
                if (match.index > lastIndex) {
                    fragment.appendChild(document.createTextNode(text.substring(lastIndex, match.index)));
                }

                // 创建并添加高亮元素
                const highlightSpan = document.createElement('span');
                highlightSpan.className = 'search-highlight';
                highlightSpan.textContent = match[0];
                fragment.appendChild(highlightSpan);
                matches.push(highlightSpan);
                nodeMatches++;

                lastIndex = regex.lastIndex;
            }

            // 添加匹配后的剩余文本
            if (lastIndex < text.length) {
                fragment.appendChild(document.createTextNode(text.substring(lastIndex)));
            }

            // 替换原文本节点
            if (fragment.childNodes.length > 0) {
                textNode.parentNode.replaceChild(fragment, textNode);
                processedNodes++;
            }

            console.log(`节点处理完成，匹配数: ${nodeMatches}`);
        });

        console.log(`高亮处理完成: 处理了 ${processedNodes} 个节点，总匹配数: ${matches.length}`);
        return matches;
    }

    /**
     * 更新搜索UI（计数器和按钮状态）
     */
    function updateSearchUI(toolPrefix) {
        const container = document.getElementById(`${toolPrefix}-tool`);
        const state = searchState[toolPrefix];

        const countSpan = container.querySelector('.search-count');
        const prevBtn = container.querySelector('.search-prev-btn');
        const nextBtn = container.querySelector('.search-next-btn');

        const total = state.matches.length;
        const current = state.currentIndex + 1;

        countSpan.textContent = `${current} / ${total}`;

        prevBtn.disabled = total === 0 || current <= 1;
        nextBtn.disabled = total === 0 || current === total;
    }

    /**
     * 导航到指定的匹配项
     */
    function navigateToMatch(toolPrefix, direction) {
        const state = searchState[toolPrefix];
        if (state.matches.length === 0) return;

        // 移除旧的当前匹配样式
        if (state.currentIndex !== -1 && state.matches[state.currentIndex]) {
            state.matches[state.currentIndex].classList.remove('current-match');
        }

        // 计算新索引
        if (direction === 'next') {
            state.currentIndex = (state.currentIndex + 1) % state.matches.length;
        } else if (direction === 'prev') {
            state.currentIndex = (state.currentIndex - 1 + state.matches.length) % state.matches.length;
        } else if (direction === 'first') {
            state.currentIndex = 0;
        }

        // 添加新的当前匹配样式并滚动
        const currentMatchEl = state.matches[state.currentIndex];
        if (currentMatchEl) {
            currentMatchEl.classList.add('current-match');
            currentMatchEl.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        updateSearchUI(toolPrefix);
    }
    const mainElement = document.querySelector('main');
    if (mainElement) {
        mainElement.addEventListener('click', (e) => {
            const toggler = e.target.closest('.toggler');
            if (toggler) {
                const icon = toggler.querySelector('i');
                const content = toggler.nextElementSibling;
                if (content && icon) {
                    const isHidden = content.style.display === 'none';
                    content.style.display = isHidden ? 'block' : 'none';
                    icon.classList.toggle('fa-plus-square', !isHidden);
                    icon.classList.toggle('fa-minus-square', isHidden);
                }
            }
        });
    }


    // --- 文本对比工具逻辑 ---
    // 专为Python爬虫开发者设计的示例数据
    const TEXT_COMPARE_EXAMPLES = {
        json: {
            name: "JSON数据对比",
            textA: `{
    "status": "success",
    "data": {
        "user_id": 12345,
        "username": "spider_user",
        "email": "<EMAIL>",
        "profile": {
            "name": "Spider User",
            "age": 25,
            "location": "Beijing"
        },
        "permissions": ["read", "write"]
    }
}`,
            textB: `{
    "status": "success",
    "data": {
        "user_id": 12345,
        "username": "spider_user",
        "email": "<EMAIL>",
        "profile": {
            "name": "Spider User",
            "age": 26,
            "location": "Shanghai",
            "phone": "+86-138-0000-0000"
        },
        "permissions": ["read", "write", "admin"]
    },
    "version": "2.0"
}`
        },
        text: {
            name: "文本数据对比",
            textA: `产品名称: iPhone 15 Pro
价格: ¥7999
库存: 现货充足
评分: 4.8分
描述: 全新A17 Pro芯片
标签: 手机,苹果,5G
发布时间: 2023-09-15`,
            textB: `产品名称: iPhone 15 Pro Max
价格: ¥9999
库存: 现货充足
评分: 4.9分
描述: 全新A17 Pro芯片，钛金属设计
标签: 手机,苹果,5G,高端
发布时间: 2023-09-15
促销: 限时优惠`
        },
        config: {
            name: "配置文件对比",
            textA: `# 爬虫配置 - 开发环境
SPIDER_NAME = "product_spider"
BASE_URL = "https://dev-api.example.com"
REQUEST_DELAY = 1
CONCURRENT_REQUESTS = 8
RETRY_TIMES = 3
TIMEOUT = 30

# 数据库配置
DB_HOST = "localhost"
DB_PORT = 3306
DB_NAME = "spider_dev"

# 代理设置
USE_PROXY = False
LOG_LEVEL = "DEBUG"`,
            textB: `# 爬虫配置 - 生产环境
SPIDER_NAME = "product_spider"
BASE_URL = "https://api.example.com"
REQUEST_DELAY = 2
CONCURRENT_REQUESTS = 16
RETRY_TIMES = 5
TIMEOUT = 60

# 数据库配置
DB_HOST = "prod-db.example.com"
DB_PORT = 3306
DB_NAME = "spider_prod"
DB_PASSWORD = "***"

# 代理设置
USE_PROXY = True
PROXY_LIST = ["proxy1.com:8080"]
LOG_LEVEL = "INFO"`
        }
    };

    // --- 文本转换工具逻辑 ---
    function initTextConvertTool() {
        // 选项卡切换功能
        const tabButtons = document.querySelectorAll('.text-convert-tab-btn');
        const tabContents = document.querySelectorAll('.text-convert-tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const targetTab = button.getAttribute('data-tab');

                // 更新按钮状态
                tabButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');

                // 更新内容显示
                tabContents.forEach(content => {
                    if (content.id === targetTab) {
                        content.classList.remove('tab-hidden');
                    } else {
                        content.classList.add('tab-hidden');
                    }
                });
            });
        });

        // 初始化各个功能模块
        initHtmlEntityConverter();
        initUrlEncoder();
        initUnicodeConverter();
    }



    // 显示Unicode字符信息（使用后端返回的数据）
    function displayUnicodeCharInfo(charInfoList) {
        const unicodeCharDetails = document.getElementById('unicode-char-details');
        if (!unicodeCharDetails || !charInfoList || charInfoList.length === 0) {
            if (unicodeCharDetails) {
                unicodeCharDetails.innerHTML = `
                    <div class="text-center text-gray-400 py-4">
                        输入文本后显示字符详细信息
                    </div>`;
            }
            return;
        }

        let html = '';
        charInfoList.forEach(charInfo => {
            html += `
                <div class="unicode-char-item">
                    <div class="flex items-center justify-between">
                        <span class="char">${charInfo.char}</span>
                        <div class="text-right">
                            <div class="code">${charInfo.unicode_code}</div>
                            <div class="code">\\u${charInfo.hex_code.padStart(4, '0')}</div>
                            <div class="code">&#${charInfo.decimal_code};</div>
                        </div>
                    </div>
                    <div class="name mt-1">${charInfo.name}</div>
                </div>
            `;
        });

        unicodeCharDetails.innerHTML = html;
    }

    // HTML实体编码/解码功能
    function initHtmlEntityConverter() {
        const htmlInput = document.getElementById('html-entity-input');
        const htmlOutput = document.getElementById('html-entity-output');
        const encodeBtn = document.getElementById('html-encode-btn');
        const decodeBtn = document.getElementById('html-decode-btn');
        const clearBtn = document.getElementById('clear-html-entity-btn');
        const copyBtn = document.getElementById('copy-html-entity-btn');
        const exampleBtn = document.getElementById('load-html-example-btn');

        if (encodeBtn) {
            encodeBtn.addEventListener('click', async () => {
                const text = htmlInput.value;
                if (!text.trim()) {
                    showTextConvertMessage('请输入要编码的文本', 'error');
                    return;
                }

                try {
                    // 调用后端API进行HTML实体编码
                    const response = await fetch('/api/v1/text-convert/html-entity', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'encode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        htmlOutput.value = result.result;
                        showTextConvertMessage('HTML实体编码完成', 'success');
                    } else {
                        throw new Error(result.error || '编码失败');
                    }
                } catch (error) {
                    showTextConvertMessage('编码失败: ' + error.message, 'error');
                }
            });
        }

        if (decodeBtn) {
            decodeBtn.addEventListener('click', async () => {
                const text = htmlInput.value;
                if (!text.trim()) {
                    showTextConvertMessage('请输入要解码的HTML实体', 'error');
                    return;
                }

                try {
                    // 调用后端API进行HTML实体解码
                    const response = await fetch('/api/v1/text-convert/html-entity', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'decode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        htmlOutput.value = result.result;
                        showTextConvertMessage('HTML实体解码完成', 'success');
                    } else {
                        throw new Error(result.error || '解码失败');
                    }
                } catch (error) {
                    showTextConvertMessage('解码失败: ' + error.message, 'error');
                }
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                htmlInput.value = '';
                htmlOutput.value = '';
            });
        }

        if (copyBtn) {
            copyBtn.addEventListener('click', () => {
                if (htmlOutput.value) {
                    navigator.clipboard.writeText(htmlOutput.value)
                        .then(() => window.showCopyModal())
                        .catch(err => console.error('复制失败:', err));
                }
            });
        }

        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => {
                htmlInput.value = MOCK_HTML_ENTITY_TEXT;
                showTextConvertMessage('已载入示例数据', 'success');
            });
        }
    }



    // URL编码/解码功能
    function initUrlEncoder() {
        const urlInput = document.getElementById('url-encode-input');
        const urlOutput = document.getElementById('url-encode-output');
        const encodeBtn = document.getElementById('url-encode-btn');
        const decodeBtn = document.getElementById('url-decode-btn');
        const clearBtn = document.getElementById('clear-url-encode-btn');
        const copyBtn = document.getElementById('copy-url-encode-btn');
        const exampleBtn = document.getElementById('load-url-example-btn');

        if (encodeBtn) {
            encodeBtn.addEventListener('click', async () => {
                const text = urlInput.value;
                if (!text.trim()) {
                    showTextConvertMessage('请输入要编码的文本', 'error');
                    return;
                }

                try {
                    // 调用后端API进行URL编码
                    const response = await fetch('/api/v1/text-convert/url-encode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'encode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        urlOutput.value = result.result;
                        showTextConvertMessage('URL编码完成', 'success');
                    } else {
                        throw new Error(result.error || '编码失败');
                    }
                } catch (error) {
                    showTextConvertMessage('编码失败: ' + error.message, 'error');
                }
            });
        }

        if (decodeBtn) {
            decodeBtn.addEventListener('click', async () => {
                const text = urlInput.value;
                if (!text.trim()) {
                    showTextConvertMessage('请输入要解码的URL编码', 'error');
                    return;
                }

                try {
                    // 调用后端API进行URL解码
                    const response = await fetch('/api/v1/text-convert/url-encode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'decode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        urlOutput.value = result.result;
                        showTextConvertMessage('URL解码完成', 'success');
                    } else {
                        throw new Error(result.error || '解码失败');
                    }
                } catch (error) {
                    showTextConvertMessage('解码失败: ' + error.message, 'error');
                }
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                urlInput.value = '';
                urlOutput.value = '';
            });
        }

        if (copyBtn) {
            copyBtn.addEventListener('click', () => {
                if (urlOutput.value) {
                    navigator.clipboard.writeText(urlOutput.value)
                        .then(() => window.showCopyModal())
                        .catch(err => console.error('复制失败:', err));
                }
            });
        }

        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => {
                urlInput.value = MOCK_URL_ENCODE_TEXT;
                showTextConvertMessage('已载入示例数据', 'success');
            });
        }
    }

    // Unicode编码/解码功能
    function initUnicodeConverter() {
        const unicodeInput = document.getElementById('unicode-input');
        const unicodeOutput = document.getElementById('unicode-output');
        const encodeBtn = document.getElementById('unicode-encode-btn');
        const decodeBtn = document.getElementById('unicode-decode-btn');
        const clearBtn = document.getElementById('clear-unicode-btn');
        const copyBtn = document.getElementById('copy-unicode-btn');
        const exampleBtn = document.getElementById('load-unicode-example-btn');
        const unicodeCharDetails = document.getElementById('unicode-char-details');

        if (encodeBtn) {
            encodeBtn.addEventListener('click', async () => {
                const text = unicodeInput.value;
                if (!text.trim()) {
                    showTextConvertMessage('请输入要编码的文本', 'error');
                    return;
                }

                try {
                    const format = document.querySelector('input[name="unicode-format"]:checked').value;

                    // 调用后端API进行Unicode编码
                    const response = await fetch('/api/v1/text-convert/unicode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            operation: 'encode',
                            format: format
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        unicodeOutput.value = result.result;
                        displayUnicodeCharInfo(result.char_info);
                        showTextConvertMessage('Unicode编码完成', 'success');
                    } else {
                        throw new Error(result.error || '编码失败');
                    }
                } catch (error) {
                    showTextConvertMessage('编码失败: ' + error.message, 'error');
                }
            });
        }

        if (decodeBtn) {
            decodeBtn.addEventListener('click', async () => {
                const text = unicodeInput.value;
                if (!text.trim()) {
                    showTextConvertMessage('请输入要解码的Unicode编码', 'error');
                    return;
                }

                try {
                    // 调用后端API进行Unicode解码
                    const response = await fetch('/api/v1/text-convert/unicode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            operation: 'decode',
                            format: '\\u'  // 解码时格式不重要
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const result = await response.json();

                    if (result.success) {
                        unicodeOutput.value = result.result;
                        displayUnicodeCharInfo(result.char_info);
                        showTextConvertMessage('Unicode解码完成', 'success');
                    } else {
                        throw new Error(result.error || '解码失败');
                    }
                } catch (error) {
                    showTextConvertMessage('解码失败: ' + error.message, 'error');
                }
            });
        }

        if (clearBtn) {
            clearBtn.addEventListener('click', () => {
                unicodeInput.value = '';
                unicodeOutput.value = '';
                unicodeCharDetails.innerHTML = `
                    <div class="text-center text-gray-400 py-4">
                        输入文本后显示字符详细信息
                    </div>`;
            });
        }

        if (copyBtn) {
            copyBtn.addEventListener('click', () => {
                if (unicodeOutput.value) {
                    navigator.clipboard.writeText(unicodeOutput.value)
                        .then(() => window.showCopyModal())
                        .catch(err => console.error('复制失败:', err));
                }
            });
        }

        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => {
                unicodeInput.value = MOCK_UNICODE_TEXT;
                showTextConvertMessage('已载入示例数据', 'success');
            });
        }

        // 实时更新字符信息
        if (unicodeInput) {
            unicodeInput.addEventListener('input', async () => {
                const text = unicodeInput.value;
                if (text && text.length <= 50) { // 限制长度避免频繁请求
                    try {
                        const response = await fetch('/api/v1/text-convert/unicode', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({
                                text: text,
                                operation: 'encode',
                                format: '\\u'
                            })
                        });

                        if (response.ok) {
                            const result = await response.json();
                            if (result.success) {
                                displayUnicodeCharInfo(result.char_info);
                            }
                        }
                    } catch (error) {
                        console.log('实时更新字符信息失败:', error);
                    }
                }
            });
        }
    }



    // 显示文本转换消息
    function showTextConvertMessage(message, type = 'info') {
        const messageArea = document.getElementById('text-convert-message-area');
        if (!messageArea) return;

        messageArea.textContent = message;
        messageArea.className = `text-center text-sm h-5 mb-2 flex-shrink-0 ${
            type === 'error' ? 'text-red-600' :
            type === 'success' ? 'text-green-600' :
            'text-blue-600'
        }`;

        // 3秒后清除消息
        setTimeout(() => {
            messageArea.textContent = '';
            messageArea.className = 'text-center text-sm text-red-600 h-5 mb-2 flex-shrink-0';
        }, 3000);
    }

    // --- 父子级导航功能 ---
    function initParentChildNavigation() {
        const parentHeaders = document.querySelectorAll('.nav-parent-header');

        parentHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const parentId = header.getAttribute('data-parent');
                const children = document.querySelector(`[data-parent-children="${parentId}"]`);
                const arrow = header.querySelector('.nav-parent-arrow');

                // 移除其他父级的active状态
                document.querySelectorAll('.nav-parent-header').forEach(h => h.classList.remove('active'));
                document.querySelectorAll('.sidebar-link').forEach(link => link.classList.remove('active'));

                // 添加当前父级的active状态
                header.classList.add('active');

                if (children) {
                    const isCollapsed = children.classList.contains('collapsed');

                    if (isCollapsed) {
                        // 展开
                        children.classList.remove('collapsed');
                        arrow.classList.remove('rotated');
                        localStorage.setItem(`nav-${parentId}-expanded`, 'true');
                    } else {
                        // 折叠
                        children.classList.add('collapsed');
                        arrow.classList.add('rotated');
                        localStorage.setItem(`nav-${parentId}-expanded`, 'false');
                    }
                }
            });
        });

        // 恢复保存的展开/折叠状态
        parentHeaders.forEach(header => {
            const parentId = header.getAttribute('data-parent');
            const children = document.querySelector(`[data-parent-children="${parentId}"]`);
            const arrow = header.querySelector('.nav-parent-arrow');
            const savedState = localStorage.getItem(`nav-${parentId}-expanded`);

            // 默认展开，除非明确保存为折叠状态
            if (savedState === 'false') {
                children?.classList.add('collapsed');
                arrow?.classList.add('rotated');
            }
        });
    }

    // --- 编码解码工具初始化 ---
    function initEncodingTools() {

        initHtmlEntityTool();
        initUrlEncodeTool();
        initUnicodeTool();
    }



    // HTML实体编码工具
    function initHtmlEntityTool() {
        const encodeBtn = document.getElementById('html-entity-encode-btn');
        const decodeBtn = document.getElementById('html-entity-decode-btn');
        const input = document.getElementById('html-entity-input');
        const output = document.getElementById('html-entity-output');
        const clearInputBtn = document.getElementById('clear-html-entity-input-btn');
        const clearOutputBtn = document.getElementById('clear-html-entity-output-btn');
        const copyInputBtn = document.getElementById('copy-html-entity-input-btn');
        const copyOutputBtn = document.getElementById('copy-html-entity-output-btn');
        const exampleBtn = document.getElementById('load-html-entity-example-btn');
        const encodedExampleBtn = document.getElementById('load-html-entity-encoded-example-btn');

        if (encodeBtn) {
            encodeBtn.addEventListener('click', async () => {
                const text = input.value;
                if (!text.trim()) {
                    showMessage('请输入要编码的文本', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/text-convert/html-entity', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'encode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        output.value = data.result;
                        showMessage('HTML实体编码完成', 'success');
                    } else {
                        throw new Error(data.error || '编码失败');
                    }
                } catch (error) {
                    showMessage('编码失败: ' + error.message, 'error');
                }
            });
        }

        if (decodeBtn) {
            decodeBtn.addEventListener('click', async () => {
                const text = output.value;
                if (!text.trim()) {
                    showMessage('请输入要解码的HTML实体', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/text-convert/html-entity', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'decode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        input.value = data.result;
                        showMessage('HTML实体解码完成', 'success');
                    } else {
                        throw new Error(data.error || '解码失败');
                    }
                } catch (error) {
                    showMessage('解码失败: ' + error.message, 'error');
                }
            });
        }

        // 清空和复制按钮
        if (clearInputBtn) clearInputBtn.addEventListener('click', () => input.value = '');
        if (clearOutputBtn) clearOutputBtn.addEventListener('click', () => output.value = '');
        if (copyInputBtn) copyInputBtn.addEventListener('click', () => copyToClipboard(input.value));
        if (copyOutputBtn) copyOutputBtn.addEventListener('click', () => copyToClipboard(output.value));

        // 示例按钮
        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => {
                input.value = ENCODING_EXAMPLES.htmlEntity.original;
                showMessage('已载入原始示例', 'success');
            });
        }

        if (encodedExampleBtn) {
            encodedExampleBtn.addEventListener('click', () => {
                output.value = ENCODING_EXAMPLES.htmlEntity.encoded;
                showMessage('已载入编码示例', 'success');
            });
        }
    }

    // URL编码工具
    function initUrlEncodeTool() {
        const encodeBtn = document.getElementById('url-encode-btn');
        const decodeBtn = document.getElementById('url-decode-btn');
        const input = document.getElementById('url-input');
        const output = document.getElementById('url-output');
        const clearInputBtn = document.getElementById('clear-url-input-btn');
        const clearOutputBtn = document.getElementById('clear-url-output-btn');
        const copyInputBtn = document.getElementById('copy-url-input-btn');
        const copyOutputBtn = document.getElementById('copy-url-output-btn');
        const exampleBtn = document.getElementById('load-url-example-btn');
        const encodedExampleBtn = document.getElementById('load-url-encoded-example-btn');

        if (encodeBtn) {
            encodeBtn.addEventListener('click', async () => {
                const text = input.value;
                if (!text.trim()) {
                    showMessage('请输入要编码的文本', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/text-convert/url-encode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'encode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        output.value = data.result;
                        showMessage('URL编码完成', 'success');
                    } else {
                        throw new Error(data.error || '编码失败');
                    }
                } catch (error) {
                    showMessage('编码失败: ' + error.message, 'error');
                }
            });
        }

        if (decodeBtn) {
            decodeBtn.addEventListener('click', async () => {
                const text = output.value;
                if (!text.trim()) {
                    showMessage('请输入要解码的URL编码', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/text-convert/url-encode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ text: text, operation: 'decode' })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        input.value = data.result;
                        showMessage('URL解码完成', 'success');
                    } else {
                        throw new Error(data.error || '解码失败');
                    }
                } catch (error) {
                    showMessage('解码失败: ' + error.message, 'error');
                }
            });
        }

        // 清空和复制按钮
        if (clearInputBtn) clearInputBtn.addEventListener('click', () => input.value = '');
        if (clearOutputBtn) clearOutputBtn.addEventListener('click', () => output.value = '');
        if (copyInputBtn) copyInputBtn.addEventListener('click', () => copyToClipboard(input.value));
        if (copyOutputBtn) copyOutputBtn.addEventListener('click', () => copyToClipboard(output.value));

        // 示例按钮
        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => {
                input.value = ENCODING_EXAMPLES.urlEncode.original;
                showMessage('已载入原始示例', 'success');
            });
        }

        if (encodedExampleBtn) {
            encodedExampleBtn.addEventListener('click', () => {
                output.value = ENCODING_EXAMPLES.urlEncode.encoded;
                showMessage('已载入编码示例', 'success');
            });
        }
    }

    // Unicode编码工具
    function initUnicodeTool() {
        const encodeBtn = document.getElementById('unicode-encode-btn');
        const decodeBtn = document.getElementById('unicode-decode-btn');
        const input = document.getElementById('unicode-input');
        const output = document.getElementById('unicode-output');
        const clearInputBtn = document.getElementById('clear-unicode-input-btn');
        const clearOutputBtn = document.getElementById('clear-unicode-output-btn');
        const copyInputBtn = document.getElementById('copy-unicode-input-btn');
        const copyOutputBtn = document.getElementById('copy-unicode-output-btn');
        const exampleBtn = document.getElementById('load-unicode-example-btn');
        const encodedExampleBtn = document.getElementById('load-unicode-encoded-example-btn');
        const charDetails = document.getElementById('unicode-char-details');

        if (encodeBtn) {
            encodeBtn.addEventListener('click', async () => {
                const text = input.value;
                if (!text.trim()) {
                    showMessage('请输入要编码的文本', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/text-convert/unicode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            operation: 'encode',
                            format: '\\u'  // 固定使用\u格式
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        output.value = data.result;
                        showMessage('Unicode编码完成', 'success');
                    } else {
                        throw new Error(data.error || '编码失败');
                    }
                } catch (error) {
                    showMessage('编码失败: ' + error.message, 'error');
                }
            });
        }

        if (decodeBtn) {
            decodeBtn.addEventListener('click', async () => {
                // 优先从右侧读取，如果右侧为空则从左侧读取
                let text = output.value.trim();
                if (!text) {
                    text = input.value.trim();
                }

                if (!text) {
                    showMessage('请输入要解码的Unicode编码', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/text-convert/unicode', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            text: text,
                            operation: 'decode',
                            format: '\\u'
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.success) {
                        // 如果是从右侧解码，结果放到左侧；如果是从左侧解码，结果放到右侧
                        if (output.value.trim()) {
                            input.value = data.result;
                        } else {
                            output.value = data.result;
                        }
                        showMessage('Unicode解码完成', 'success');
                    } else {
                        throw new Error(data.error || '解码失败');
                    }
                } catch (error) {
                    showMessage('解码失败: ' + error.message, 'error');
                }
            });
        }

        // 清空和复制按钮
        if (clearInputBtn) clearInputBtn.addEventListener('click', () => input.value = '');
        if (clearOutputBtn) clearOutputBtn.addEventListener('click', () => output.value = '');
        if (copyInputBtn) copyInputBtn.addEventListener('click', () => copyToClipboard(input.value));
        if (copyOutputBtn) copyOutputBtn.addEventListener('click', () => copyToClipboard(output.value));

        // 示例按钮
        if (exampleBtn) {
            exampleBtn.addEventListener('click', () => {
                input.value = ENCODING_EXAMPLES.unicode.original;
                showMessage('已载入原始示例', 'success');
            });
        }

        if (encodedExampleBtn) {
            encodedExampleBtn.addEventListener('click', () => {
                output.value = ENCODING_EXAMPLES.unicode.encoded;
                showMessage('已载入编码示例', 'success');
            });
        }
    }

    // 辅助函数
    function copyToClipboard(text) {
        if (text) {
            navigator.clipboard.writeText(text)
                .then(() => window.showCopyModal())
                .catch(err => console.error('复制失败:', err));
        }
    }

    function showMessage(message, type = 'info') {
        // 尝试多个可能的消息区域
        const messageAreas = [
            document.getElementById('html-entity-input-message'),
            document.getElementById('url-input-message'),
            document.getElementById('unicode-input-message'),
            document.getElementById('timestamp-input-message'),
            document.getElementById('date-input-message'),
            document.getElementById('date-diff-message'),
            document.getElementById('date-offset-message')
        ];

        messageAreas.forEach(area => {
            if (area) {
                area.textContent = message;
                area.className = `mt-2 text-sm h-5 flex-shrink-0 ${
                    type === 'error' ? 'text-red-600' :
                    type === 'success' ? 'text-green-600' :
                    'text-blue-600'
                }`;

                setTimeout(() => {
                    area.textContent = '';
                    area.className = 'mt-2 text-sm text-gray-500 h-5 flex-shrink-0';
                }, 3000);
            }
        });
    }

    // --- 时间转换工具初始化 ---
    function initTimestampTool() {
        const tsToDateBtn = document.getElementById('ts-to-date-btn');
        const dateToTsBtn = document.getElementById('date-to-ts-btn');
        const clearTimestampBtn = document.getElementById('clear-timestamp-btn');
        const clearDateBtn = document.getElementById('clear-date-btn');
        const loadTimestampExampleBtn = document.getElementById('load-timestamp-example-btn');
        const loadDateExampleBtn = document.getElementById('load-date-example-btn');

        // 实时显示当前时间
        function updateCurrentTime() {
            const now = new Date();
            const timestampSeconds = Math.floor(now.getTime() / 1000);
            const timestampMilliseconds = now.getTime();

            // 格式化日期时间
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const milliseconds = String(now.getMilliseconds()).padStart(3, '0');

            const dateTimeFullStr = `${year}年${month}月${day}日${hours}点${minutes}分${seconds}秒`;
            const dateTimeMillisecondsStr = `${year}年${month}月${day}日${hours}点${minutes}分${seconds}秒${milliseconds}毫秒`;

            // 更新实时时间戳显示
            const currentTimestampSeconds = document.getElementById('current-timestamp-seconds');
            const currentTimestampMilliseconds = document.getElementById('current-timestamp-milliseconds');
            const currentDateTimeFull = document.getElementById('current-datetime-full');
            const currentDateTimeMilliseconds = document.getElementById('current-datetime-milliseconds');

            if (currentTimestampSeconds) {
                currentTimestampSeconds.textContent = timestampSeconds;
            }
            if (currentTimestampMilliseconds) {
                currentTimestampMilliseconds.textContent = timestampMilliseconds;
            }
            if (currentDateTimeFull) {
                currentDateTimeFull.textContent = dateTimeFullStr;
            }
            if (currentDateTimeMilliseconds) {
                currentDateTimeMilliseconds.textContent = dateTimeMillisecondsStr;
            }

        }

        // 每秒更新时间
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();

        // 添加复制功能 - 设为全局函数
        window.copyToClipboard = function(text, message) {
            navigator.clipboard.writeText(text).then(() => {
                showCopyModal(message);
            }).catch(() => {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showCopyModal(message);
            });
        };

        // 复制模态框
        function showCopyModal(message) {
            // 创建模态框
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.innerHTML = `
                <div class="bg-white rounded-lg p-6 max-w-sm mx-4 transform transition-all duration-300 scale-95 opacity-0">
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-medium text-gray-900">复制成功</h3>
                            <p class="text-sm text-gray-600">${message}</p>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);

            // 显示动画
            setTimeout(() => {
                const content = modal.querySelector('div > div');
                content.classList.remove('scale-95', 'opacity-0');
                content.classList.add('scale-100', 'opacity-100');
            }, 10);

            // 自动关闭
            setTimeout(() => {
                const content = modal.querySelector('div > div');
                content.classList.add('scale-95', 'opacity-0');
                setTimeout(() => {
                    document.body.removeChild(modal);
                }, 300);
            }, 2000);
        }

        // 绑定时间戳复制事件
        const currentTimestampSeconds = document.getElementById('current-timestamp-seconds');
        const currentTimestampMilliseconds = document.getElementById('current-timestamp-milliseconds');
        const currentDateTimeFull = document.getElementById('current-datetime-full');
        const currentDateTimeMilliseconds = document.getElementById('current-datetime-milliseconds');

        if (currentTimestampSeconds) {
            currentTimestampSeconds.addEventListener('click', () => {
                copyToClipboard(currentTimestampSeconds.textContent, '已复制秒级时间戳到剪切板');
            });
        }

        if (currentTimestampMilliseconds) {
            currentTimestampMilliseconds.addEventListener('click', () => {
                copyToClipboard(currentTimestampMilliseconds.textContent, '已复制毫秒级时间戳到剪切板');
            });
        }

        if (currentDateTimeFull) {
            currentDateTimeFull.addEventListener('click', () => {
                copyToClipboard(currentDateTimeFull.textContent, '已复制当前日期时间到剪切板');
            });
        }

        if (currentDateTimeMilliseconds) {
            currentDateTimeMilliseconds.addEventListener('click', () => {
                copyToClipboard(currentDateTimeMilliseconds.textContent, '已复制当前日期时间（毫秒）到剪切板');
            });
        }

        // 添加键盘快捷键支持
        document.addEventListener('keydown', (e) => {
            // 只在时间戳工具激活时响应快捷键
            if (!document.getElementById('timestamp-tool').classList.contains('hidden')) {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 'Enter':
                            e.preventDefault();
                            // 根据当前焦点元素决定执行哪个转换
                            const activeElement = document.activeElement;
                            if (activeElement && activeElement.id === 'ts-to-date-input') {
                                tsToDateBtn.click();
                            } else if (activeElement && activeElement.id === 'date-to-ts-input') {
                                dateToTsBtn.click();
                            }
                            break;
                        case 'l':
                            e.preventDefault();
                            // 载入示例
                            const activeInput = document.activeElement;
                            if (activeInput && activeInput.id === 'ts-to-date-input') {
                                loadTimestampExampleBtn.click();
                            } else if (activeInput && activeInput.id === 'date-to-ts-input') {
                                loadDateExampleBtn.click();
                            }
                            break;
                        case 'k':
                            e.preventDefault();
                            // 清空
                            const currentInput = document.activeElement;
                            if (currentInput && currentInput.id === 'ts-to-date-input') {
                                clearTimestampBtn.click();
                            } else if (currentInput && currentInput.id === 'date-to-ts-input') {
                                clearDateBtn.click();
                            }
                            break;
                    }
                }
            }
        });

        // 添加输入框增强功能
        const tsInput = document.getElementById('ts-to-date-input');
        const dateInput = document.getElementById('date-to-ts-input');

        if (tsInput) {
            // 时间戳输入框只允许数字
            tsInput.addEventListener('input', (e) => {
                const value = e.target.value;
                const numericValue = value.replace(/[^0-9]/g, '');
                if (value !== numericValue) {
                    e.target.value = numericValue;
                    showMessage('时间戳只能包含数字', 'warning');
                }
            });

            // 回车键快速转换
            tsInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    tsToDateBtn.click();
                }
            });
        }

        if (dateInput) {
            // 回车键快速转换
            dateInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    dateToTsBtn.click();
                }
            });
        }

        // 载入示例功能
        if (loadTimestampExampleBtn) {
            loadTimestampExampleBtn.addEventListener('click', () => {
                const input = document.getElementById('ts-to-date-input');
                if (input) {
                    // 载入当前时间戳作为示例
                    const now = Math.floor(Date.now() / 1000);
                    input.value = now.toString();
                    showMessage('已载入当前时间戳示例', 'success');
                }
            });
        }

        if (loadDateExampleBtn) {
            loadDateExampleBtn.addEventListener('click', () => {
                const input = document.getElementById('date-to-ts-input');
                if (input) {
                    // 载入当前日期时间作为示例
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth() + 1).padStart(2, '0');
                    const day = String(now.getDate()).padStart(2, '0');
                    const hours = String(now.getHours()).padStart(2, '0');
                    const minutes = String(now.getMinutes()).padStart(2, '0');
                    const seconds = String(now.getSeconds()).padStart(2, '0');
                    input.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
                    showMessage('已载入当前日期时间示例', 'success');
                }
            });
        }

        // 清空功能
        if (clearTimestampBtn) {
            clearTimestampBtn.addEventListener('click', () => {
                const input = document.getElementById('ts-to-date-input');
                const output = document.getElementById('ts-to-date-output');
                if (input) input.value = '';
                if (output) output.innerHTML = '';
                showMessage('已清空时间戳输入', 'success');
            });
        }

        if (clearDateBtn) {
            clearDateBtn.addEventListener('click', () => {
                const input = document.getElementById('date-to-ts-input');
                const output = document.getElementById('date-to-ts-output');
                if (input) input.value = '';
                if (output) output.innerHTML = '';
                showMessage('已清空日期输入', 'success');
            });
        }

        // 时间戳转日期 - 增强版
        if (tsToDateBtn) {
            tsToDateBtn.addEventListener('click', async () => {
                const input = document.getElementById('ts-to-date-input');
                const output = document.getElementById('ts-to-date-output');
                const loadingIcon = document.getElementById('ts-loading');

                if (!input.value.trim()) {
                    showMessage('请输入时间戳', 'error');
                    input.focus();
                    input.classList.add('border-red-500');
                    setTimeout(() => input.classList.remove('border-red-500'), 2000);
                    return;
                }

                // 显示加载状态
                tsToDateBtn.disabled = true;
                tsToDateBtn.classList.add('opacity-75', 'cursor-not-allowed');
                if (loadingIcon) {
                    loadingIcon.classList.remove('hidden');
                    tsToDateBtn.querySelector('span').textContent = '转换中...';
                }

                // 添加加载动画到输出区域
                output.innerHTML = '<div class="loading-shimmer h-20 rounded-lg"></div>';

                try {
                    const response = await fetch('/api/v1/datetime/ts-to-date', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ timestamp_str: input.value })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.error) {
                        showMessage('转换失败: ' + data.error, 'error');
                        output.innerHTML = `
                            <div class="text-center py-8">
                                <i class="fas fa-exclamation-triangle text-red-500 text-3xl mb-3"></i>
                                <div class="text-red-600 font-medium">${data.error}</div>
                            </div>`;
                    } else {
                        output.innerHTML = `
                            <div class="success-animation space-y-4">
                                <!-- 识别类型 -->
                                <div class="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-4 border border-blue-200">
                                    <div class="flex items-center justify-between">
                                        <span class="text-blue-700 font-semibold text-lg">识别类型</span>
                                        <span class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-bold">${data.result.timestamp_type}</span>
                                    </div>
                                </div>

                                <!-- 时间戳结果 -->
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="bg-white rounded-xl p-5 border-2 border-gray-200 cursor-pointer hover:border-blue-400 hover:shadow-lg transition-all duration-200" onclick="copyToClipboard('${data.result.timestamp_in_seconds}', '已复制秒级时间戳到剪切板')" title="点击复制">
                                        <div class="text-gray-600 text-sm font-medium mb-2">秒级时间戳</div>
                                        <div class="font-mono text-2xl font-bold text-gray-800 break-all">${data.result.timestamp_in_seconds}</div>
                                    </div>
                                    <div class="bg-white rounded-xl p-5 border-2 border-gray-200 cursor-pointer hover:border-blue-400 hover:shadow-lg transition-all duration-200" onclick="copyToClipboard('${data.result.timestamp_in_milliseconds}', '已复制毫秒级时间戳到剪切板')" title="点击复制">
                                        <div class="text-gray-600 text-sm font-medium mb-2">毫秒级时间戳</div>
                                        <div class="font-mono text-2xl font-bold text-gray-800 break-all">${data.result.timestamp_in_milliseconds}</div>
                                    </div>
                                </div>

                                <!-- 时间显示 -->
                                <div class="space-y-3">
                                    <div class="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-5 border border-green-200">
                                        <div class="text-green-700 text-sm font-medium mb-2">本地时间</div>
                                        <div class="text-2xl font-bold text-green-800">${data.result.local_datetime}</div>
                                    </div>
                                    <div class="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-5 border border-purple-200">
                                        <div class="text-purple-700 text-sm font-medium mb-2">中文格式</div>
                                        <div class="text-2xl font-bold text-purple-800">${data.result.local_datetime_with_weekday}</div>
                                    </div>
                                </div>
                            </div>`;
                        showMessage('✨ 时间戳转换完成！', 'success');
                    }
                } catch (error) {
                    showMessage('转换失败: ' + error.message, 'error');
                    output.innerHTML = `
                        <div class="text-center py-8">
                            <i class="fas fa-wifi text-red-500 text-3xl mb-3"></i>
                            <div class="text-red-600 font-medium">网络错误，请重试</div>
                        </div>`;
                } finally {
                    // 恢复按钮状态
                    tsToDateBtn.disabled = false;
                    tsToDateBtn.classList.remove('opacity-75', 'cursor-not-allowed');
                    if (loadingIcon) {
                        loadingIcon.classList.add('hidden');
                        tsToDateBtn.querySelector('span').textContent = '转换';
                    }
                }
            });
        }

        // 日期转时间戳 - 增强版
        if (dateToTsBtn) {
            dateToTsBtn.addEventListener('click', async () => {
                const input = document.getElementById('date-to-ts-input');
                const output = document.getElementById('date-to-ts-output');
                const loadingIcon = document.getElementById('date-loading');

                if (!input.value.trim()) {
                    showMessage('请输入日期时间', 'error');
                    input.focus();
                    input.classList.add('border-red-500');
                    setTimeout(() => input.classList.remove('border-red-500'), 2000);
                    return;
                }

                // 显示加载状态
                dateToTsBtn.disabled = true;
                dateToTsBtn.classList.add('opacity-75', 'cursor-not-allowed');
                if (loadingIcon) {
                    loadingIcon.classList.remove('hidden');
                    dateToTsBtn.querySelector('span').textContent = '转换中...';
                }

                // 添加加载动画到输出区域
                output.innerHTML = '<div class="loading-shimmer h-20 rounded-lg"></div>';

                try {
                    const response = await fetch('/api/v1/datetime/date-to-ts', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ datetime_str: input.value })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.error) {
                        showMessage('转换失败: ' + data.error, 'error');
                        output.innerHTML = `
                            <div class="text-center py-8">
                                <i class="fas fa-exclamation-triangle text-red-500 text-3xl mb-3"></i>
                                <div class="text-red-600 font-medium">${data.error}</div>
                            </div>`;
                    } else {
                        output.innerHTML = `
                            <div class="success-animation space-y-3">
                                <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
                                    <div class="text-blue-700 text-sm mb-2 flex items-center">
                                        <i class="fas fa-edit mr-1"></i>原始输入
                                    </div>
                                    <div class="font-mono text-lg text-blue-800 bg-blue-100 px-3 py-2 rounded-lg">${data.result.original_input}</div>
                                </div>
                                <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
                                    <div class="text-green-700 text-sm mb-2 flex items-center">
                                        <i class="fas fa-check-circle mr-1"></i>解析结果
                                    </div>
                                    <div class="font-semibold text-xl text-green-800">${data.result.parsed_datetime}</div>
                                </div>
                                <div class="grid grid-cols-2 gap-3">
                                    <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-4 border border-gray-200 cursor-pointer hover:shadow-md transition-all" onclick="copyToClipboard('${data.result.timestamp_in_seconds}', '已复制秒级时间戳到剪切板')" title="点击复制">
                                        <div class="text-gray-600 text-sm mb-2 flex items-center">
                                            <i class="fas fa-clock mr-1"></i>秒级时间戳
                                        </div>
                                        <div class="font-mono text-xl font-bold text-gray-800">${data.result.timestamp_in_seconds}</div>
                                    </div>
                                    <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-4 border border-gray-200 cursor-pointer hover:shadow-md transition-all" onclick="copyToClipboard('${data.result.timestamp_in_milliseconds}', '已复制毫秒级时间戳到剪切板')" title="点击复制">
                                        <div class="text-gray-600 text-sm mb-2 flex items-center">
                                            <i class="fas fa-stopwatch mr-1"></i>毫秒级时间戳
                                        </div>
                                        <div class="font-mono text-xl font-bold text-gray-800">${data.result.timestamp_in_milliseconds}</div>
                                    </div>
                                </div>
                            </div>`;
                        showMessage('✨ 日期转换完成！', 'success');
                    }
                } catch (error) {
                    showMessage('转换失败: ' + error.message, 'error');
                    output.innerHTML = `
                        <div class="text-center py-8">
                            <i class="fas fa-wifi text-red-500 text-3xl mb-3"></i>
                            <div class="text-red-600 font-medium">网络错误，请重试</div>
                        </div>`;
                } finally {
                    // 恢复按钮状态
                    dateToTsBtn.disabled = false;
                    dateToTsBtn.classList.remove('opacity-75', 'cursor-not-allowed');
                    if (loadingIcon) {
                        loadingIcon.classList.add('hidden');
                        dateToTsBtn.querySelector('span').textContent = '转换';
                    }
                }
            });
        }


    }

    // --- 日期计算器工具初始化 ---
    function initDateCalculatorTool() {
        const dateDiffBtn = document.getElementById('date-diff-btn');
        const dateOffsetBtn = document.getElementById('date-offset-btn');
        const clearDateDiffBtn = document.getElementById('clear-date-diff-btn');
        const clearDateOffsetBtn = document.getElementById('clear-date-offset-btn');
        const loadDateDiffExampleBtn = document.getElementById('load-date-diff-example-btn');
        const loadDateOffsetExampleBtn = document.getElementById('load-date-offset-example-btn');

        // 实时显示当前时间
        function updateCurrentTime() {
            const now = new Date();
            const dateStr = now.getFullYear() + '-' +
                          String(now.getMonth() + 1).padStart(2, '0') + '-' +
                          String(now.getDate()).padStart(2, '0') + ' ' +
                          String(now.getHours()).padStart(2, '0') + ':' +
                          String(now.getMinutes()).padStart(2, '0') + ':' +
                          String(now.getSeconds()).padStart(2, '0');

            // 更新显示区域
            const diffOutput = document.getElementById('date-diff-output');
            const offsetOutput = document.getElementById('date-offset-output');

            if (diffOutput && !diffOutput.textContent.trim()) {
                diffOutput.innerHTML = `<div class="text-gray-500 text-xs">当前时间: ${dateStr}</div>`;
            }
            if (offsetOutput && !offsetOutput.textContent.trim()) {
                offsetOutput.innerHTML = `<div class="text-gray-500 text-xs">当前时间: ${dateStr}</div>`;
            }
        }

        // 每秒更新时间
        setInterval(updateCurrentTime, 1000);
        updateCurrentTime();

        // 设置默认值
        const now = new Date();
        const currentDateStr = now.getFullYear() + '-' +
                              String(now.getMonth() + 1).padStart(2, '0') + '-' +
                              String(now.getDate()).padStart(2, '0') + ' ' +
                              String(now.getHours()).padStart(2, '0') + ':' +
                              String(now.getMinutes()).padStart(2, '0') + ':' +
                              String(now.getSeconds()).padStart(2, '0');

        // 设置默认值
        const diffStartDate = document.getElementById('diff-start-date');
        const offsetStartDate = document.getElementById('offset-start-date');

        if (diffStartDate && !diffStartDate.value) {
            diffStartDate.value = '2020-01-01 12:00:00';
        }
        if (offsetStartDate && !offsetStartDate.value) {
            offsetStartDate.value = currentDateStr;
        }

        // 日期差计算
        if (dateDiffBtn) {
            dateDiffBtn.addEventListener('click', async () => {
                const startInput = document.getElementById('diff-start-date');
                const endInput = document.getElementById('diff-end-date');
                const output = document.getElementById('date-diff-output');

                if (!startInput.value.trim()) {
                    showMessage('请输入起始日期', 'error');
                    return;
                }

                let endDate = endInput.value.trim();
                if (!endDate) {
                    endDate = currentDateStr;
                }

                try {
                    const response = await fetch('/api/v1/datetime/diff', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            start_date: startInput.value,
                            end_date: endDate
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.error) {
                        showMessage('计算失败: ' + data.error, 'error');
                    } else {
                        output.textContent = data.result;
                        showMessage('日期差计算完成', 'success');
                    }
                } catch (error) {
                    showMessage('计算失败: ' + error.message, 'error');
                }
            });
        }

        // 日期偏移计算
        if (dateOffsetBtn) {
            dateOffsetBtn.addEventListener('click', async () => {
                const startInput = document.getElementById('offset-start-date');
                const daysInput = document.getElementById('offset-days');
                const output = document.getElementById('date-offset-output');

                if (!startInput.value.trim()) {
                    showMessage('请输入起始日期', 'error');
                    return;
                }

                try {
                    const response = await fetch('/api/v1/datetime/offset', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            start_date: startInput.value,
                            days: parseInt(daysInput.value) || 0
                        })
                    });

                    if (!response.ok) throw new Error(`HTTP ${response.status}`);
                    const data = await response.json();

                    if (data.error) {
                        showMessage('计算失败: ' + data.error, 'error');
                    } else {
                        output.textContent = data.result;
                        showMessage('日期计算完成', 'success');
                    }
                } catch (error) {
                    showMessage('计算失败: ' + error.message, 'error');
                }
            });
        }

        // 清空按钮
        if (clearDateDiffBtn) {
            clearDateDiffBtn.addEventListener('click', () => {
                document.getElementById('diff-start-date').value = '2020-01-01 12:00:00';
                document.getElementById('diff-end-date').value = '';
                document.getElementById('date-diff-output').innerHTML = '';
                updateCurrentTime();
            });
        }

        if (clearDateOffsetBtn) {
            clearDateOffsetBtn.addEventListener('click', () => {
                document.getElementById('offset-start-date').value = currentDateStr;
                document.getElementById('offset-days').value = '195';
                document.getElementById('date-offset-output').innerHTML = '';
                updateCurrentTime();
            });
        }

        // 载入示例按钮
        if (loadDateDiffExampleBtn) {
            loadDateDiffExampleBtn.addEventListener('click', () => {
                document.getElementById('diff-start-date').value = '2020-01-01 12:00:00';
                document.getElementById('diff-end-date').value = '2025-06-26 19:18:35';
                showMessage('已载入日期差计算示例', 'success');
            });
        }

        if (loadDateOffsetExampleBtn) {
            loadDateOffsetExampleBtn.addEventListener('click', () => {
                document.getElementById('offset-start-date').value = '2025-06-26 19:18:34';
                document.getElementById('offset-days').value = '195';
                showMessage('已载入日期偏移示例', 'success');
            });
        }
    }

    // 文本对比工具初始化
    function initTextCompareTool() {
        // DOM元素
        const textAEditor = document.getElementById('text-a-editor');
        const textBEditor = document.getElementById('text-b-editor');
        const textALineNumbers = document.getElementById('text-a-line-numbers');
        const textBLineNumbers = document.getElementById('text-b-line-numbers');
        const toggleLineNumbersBtn = document.getElementById('toggle-line-numbers');
        const undoBtn = document.getElementById('undo-btn');
        const clearTextABtn = document.getElementById('clear-text-a-btn');
        const clearTextBBtn = document.getElementById('clear-text-b-btn');
        const copyTextABtn = document.getElementById('copy-text-a-btn');
        const copyTextBBtn = document.getElementById('copy-text-b-btn');
        const loadJsonExampleBtn = document.getElementById('load-json-example');
        const loadTextExampleBtn = document.getElementById('load-text-example');
        const loadConfigExampleBtn = document.getElementById('load-config-example');

        // 状态管理
        let showLineNumbers = true;
        let undoStack = [];
        let isUpdating = false;
        let compareTimeout = null;

        // 保存状态到撤销栈
        function saveState() {
            const state = {
                textA: textAEditor.textContent,
                textB: textBEditor.textContent,
                timestamp: Date.now()
            };
            undoStack.push(state);
            if (undoStack.length > 50) {
                undoStack.shift();
            }
            undoBtn.disabled = false;
        }

        // 撤销操作
        function undo() {
            if (undoStack.length === 0) return;

            const state = undoStack.pop();
            isUpdating = true;
            textAEditor.textContent = state.textA;
            textBEditor.textContent = state.textB;
            isUpdating = false;

            updateLineNumbers();
            performRealTimeComparison();
            undoBtn.disabled = undoStack.length === 0;
            showMessage('已撤销操作', 'success');
        }

        // 更新行号
        function updateLineNumbers() {
            if (!showLineNumbers) {
                textALineNumbers.style.display = 'none';
                textBLineNumbers.style.display = 'none';
                return;
            }

            textALineNumbers.style.display = 'block';
            textBLineNumbers.style.display = 'block';

            const linesA = textAEditor.textContent.split('\n');
            const linesB = textBEditor.textContent.split('\n');
            const maxLines = Math.max(linesA.length, linesB.length);

            // 更新文本A行号
            let lineNumbersHtmlA = '';
            for (let i = 1; i <= maxLines; i++) {
                lineNumbersHtmlA += `<div class="line-number" data-line="${i}" data-side="a">${i}</div>`;
            }
            textALineNumbers.innerHTML = lineNumbersHtmlA;

            // 更新文本B行号
            let lineNumbersHtmlB = '';
            for (let i = 1; i <= maxLines; i++) {
                lineNumbersHtmlB += `<div class="line-number" data-line="${i}" data-side="b">${i}</div>`;
            }
            textBLineNumbers.innerHTML = lineNumbersHtmlB;
        }

        // 实时对比功能
        function performRealTimeComparison() {
            if (isUpdating) return;

            clearTimeout(compareTimeout);
            compareTimeout = setTimeout(() => {
                const textA = textAEditor.textContent;
                const textB = textBEditor.textContent;

                const linesA = textA.split('\n');
                const linesB = textB.split('\n');

                // 对比并应用样式
                applyDiffStyles(linesA, linesB);
                updateLineNumbersWithDiff(linesA, linesB);
            }, 300); // 300ms防抖
        }

        // 应用差异样式
        function applyDiffStyles(linesA, linesB) {
            const maxLines = Math.max(linesA.length, linesB.length);

            // 重新构建带样式的HTML
            let htmlA = '';
            let htmlB = '';

            for (let i = 0; i < maxLines; i++) {
                const lineA = i < linesA.length ? linesA[i] : '';
                const lineB = i < linesB.length ? linesB[i] : '';

                let classA = 'text-compare-line ';
                let classB = 'text-compare-line ';

                if (lineA === lineB) {
                    classA += 'equal';
                    classB += 'equal';
                } else if (lineA === '' && lineB !== '') {
                    classA += 'deleted';
                    classB += 'added';
                } else if (lineA !== '' && lineB === '') {
                    classA += 'deleted';
                    classB += 'added';
                } else {
                    classA += 'modified';
                    classB += 'modified';
                }

                htmlA += `<div class="${classA}">${escapeHtml(lineA)}</div>`;
                htmlB += `<div class="${classB}">${escapeHtml(lineB)}</div>`;
            }

            // 更新编辑器内容（保持光标位置）
            updateEditorWithStyles(textAEditor, htmlA);
            updateEditorWithStyles(textBEditor, htmlB);
        }

        // 更新编辑器内容并保持光标位置
        function updateEditorWithStyles(editor, html) {
            const selection = window.getSelection();
            const range = selection.rangeCount > 0 ? selection.getRangeAt(0) : null;
            let cursorOffset = 0;

            if (range && editor.contains(range.startContainer)) {
                cursorOffset = range.startOffset;
            }

            isUpdating = true;
            editor.innerHTML = html;
            isUpdating = false;

            // 恢复光标位置
            if (cursorOffset > 0) {
                try {
                    const textNode = editor.firstChild?.firstChild;
                    if (textNode && textNode.nodeType === Node.TEXT_NODE) {
                        const newRange = document.createRange();
                        newRange.setStart(textNode, Math.min(cursorOffset, textNode.textContent.length));
                        newRange.collapse(true);
                        selection.removeAllRanges();
                        selection.addRange(newRange);
                    }
                } catch (e) {
                    // 忽略光标恢复错误
                }
            }
        }

        // 更新行号并显示差异
        function updateLineNumbersWithDiff(linesA, linesB) {
            const maxLines = Math.max(linesA.length, linesB.length);

            // 更新文本A行号
            let lineNumbersHtmlA = '';
            for (let i = 0; i < maxLines; i++) {
                const lineA = i < linesA.length ? linesA[i] : '';
                const lineB = i < linesB.length ? linesB[i] : '';
                const hasDiff = lineA !== lineB;

                let className = 'line-number';
                if (hasDiff) className += ' has-diff';

                lineNumbersHtmlA += `<div class="${className}" data-line="${i + 1}" data-side="a">`;
                lineNumbersHtmlA += `${i + 1}`;

                // 添加箭头图标（A→B）
                if (hasDiff && lineA !== '') {
                    lineNumbersHtmlA += `<div class="arrow-icon arrow-right" data-line="${i + 1}" data-from="a" data-to="b" title="将此行内容复制到文本B"></div>`;
                }

                lineNumbersHtmlA += '</div>';
            }
            textALineNumbers.innerHTML = lineNumbersHtmlA;

            // 更新文本B行号
            let lineNumbersHtmlB = '';
            for (let i = 0; i < maxLines; i++) {
                const lineA = i < linesA.length ? linesA[i] : '';
                const lineB = i < linesB.length ? linesB[i] : '';
                const hasDiff = lineA !== lineB;

                let className = 'line-number';
                if (hasDiff) className += ' has-diff';

                lineNumbersHtmlB += `<div class="${className}" data-line="${i + 1}" data-side="b">`;
                lineNumbersHtmlB += `${i + 1}`;

                // 添加箭头图标（B→A）
                if (hasDiff && lineB !== '') {
                    lineNumbersHtmlB += `<div class="arrow-icon arrow-left" data-line="${i + 1}" data-from="b" data-to="a" title="将此行内容复制到文本A"></div>`;
                }

                lineNumbersHtmlB += '</div>';
            }
            textBLineNumbers.innerHTML = lineNumbersHtmlB;
        }

        // 处理箭头点击事件
        function handleArrowClick(event) {
            const arrow = event.target.closest('.arrow-icon');
            if (!arrow) return;

            event.stopPropagation();

            const lineNum = parseInt(arrow.dataset.line);
            const fromSide = arrow.dataset.from;
            const toSide = arrow.dataset.to;

            // 保存状态用于撤销
            saveState();

            const linesA = textAEditor.textContent.split('\n');
            const linesB = textBEditor.textContent.split('\n');

            if (fromSide === 'a' && toSide === 'b') {
                // A→B：将A的内容复制到B
                if (lineNum <= linesA.length) {
                    linesB[lineNum - 1] = linesA[lineNum - 1];
                }
            } else if (fromSide === 'b' && toSide === 'a') {
                // B→A：将B的内容复制到A
                if (lineNum <= linesB.length) {
                    linesA[lineNum - 1] = linesB[lineNum - 1];
                }
            }

            // 更新编辑器内容
            isUpdating = true;
            textAEditor.textContent = linesA.join('\n');
            textBEditor.textContent = linesB.join('\n');
            isUpdating = false;

            // 重新对比
            performRealTimeComparison();
            showMessage(`已将第${lineNum}行内容从${fromSide.toUpperCase()}复制到${toSide.toUpperCase()}`, 'success');
        }

        // 载入示例数据
        function loadExample(type) {
            const example = TEXT_COMPARE_EXAMPLES[type];
            if (!example) return;

            saveState();

            isUpdating = true;
            textAEditor.textContent = example.textA;
            textBEditor.textContent = example.textB;
            isUpdating = false;

            updateLineNumbers();
            performRealTimeComparison();
            showMessage(`已载入${example.name}`, 'success');
        }

        // 切换行号显示
        function toggleLineNumbers() {
            showLineNumbers = !showLineNumbers;

            if (showLineNumbers) {
                textALineNumbers.style.display = 'block';
                textBLineNumbers.style.display = 'block';
                toggleLineNumbersBtn.innerHTML = '<i class="fas fa-list-ol mr-2"></i>显示行号';
                toggleLineNumbersBtn.classList.remove('bg-gray-100', 'text-gray-700');
                toggleLineNumbersBtn.classList.add('bg-blue-100', 'text-blue-700');
            } else {
                textALineNumbers.style.display = 'none';
                textBLineNumbers.style.display = 'none';
                toggleLineNumbersBtn.innerHTML = '<i class="fas fa-list-ol mr-2"></i>隐藏行号';
                toggleLineNumbersBtn.classList.remove('bg-blue-100', 'text-blue-700');
                toggleLineNumbersBtn.classList.add('bg-gray-100', 'text-gray-700');
            }

            updateLineNumbers();
        }

        // 清空文本
        function clearText(side) {
            saveState();

            isUpdating = true;
            if (side === 'a') {
                textAEditor.textContent = '';
            } else {
                textBEditor.textContent = '';
            }
            isUpdating = false;

            updateLineNumbers();
            performRealTimeComparison();
            showMessage(`已清空文本${side.toUpperCase()}`, 'success');
        }

        // 复制文本
        function copyText(side) {
            const text = side === 'a' ? textAEditor.textContent : textBEditor.textContent;
            if (!text.trim()) {
                showMessage('没有内容可复制', 'error');
                return;
            }

            navigator.clipboard.writeText(text)
                .then(() => showMessage(`文本${side.toUpperCase()}已复制到剪贴板`, 'success'))
                .catch(() => showMessage('复制失败', 'error'));
        }

        // HTML转义函数
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 显示消息
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('text-compare-message-area');
            if (messageArea) {
                messageArea.textContent = message;
                messageArea.className = `mt-2 text-sm h-5 flex-shrink-0 ${type === 'error' ? 'text-red-600' : type === 'success' ? 'text-green-600' : 'text-blue-600'}`;
                setTimeout(() => {
                    messageArea.textContent = '';
                    messageArea.className = 'mt-2 text-sm text-red-600 h-5 flex-shrink-0';
                }, 3000);
            }
        }

        // 事件监听器
        // 编辑器内容变化监听
        textAEditor.addEventListener('input', () => {
            if (!isUpdating) {
                updateLineNumbers();
                performRealTimeComparison();
            }
        });

        textBEditor.addEventListener('input', () => {
            if (!isUpdating) {
                updateLineNumbers();
                performRealTimeComparison();
            }
        });

        // 按钮事件监听
        toggleLineNumbersBtn.addEventListener('click', toggleLineNumbers);
        undoBtn.addEventListener('click', undo);
        clearTextABtn.addEventListener('click', () => clearText('a'));
        clearTextBBtn.addEventListener('click', () => clearText('b'));
        copyTextABtn.addEventListener('click', () => copyText('a'));
        copyTextBBtn.addEventListener('click', () => copyText('b'));

        // 示例按钮事件监听
        loadJsonExampleBtn.addEventListener('click', () => loadExample('json'));
        loadTextExampleBtn.addEventListener('click', () => loadExample('text'));
        loadConfigExampleBtn.addEventListener('click', () => loadExample('config'));

        // 行号和箭头点击事件监听
        textALineNumbers.addEventListener('click', handleArrowClick);
        textBLineNumbers.addEventListener('click', handleArrowClick);

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (!document.getElementById('text-compare-tool').classList.contains('hidden')) {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case 'z':
                            e.preventDefault();
                            undo();
                            break;
                        case 'l':
                            e.preventDefault();
                            toggleLineNumbers();
                            break;
                    }
                }
            }
        });

        // 初始化
        updateLineNumbers();
        loadExample('text'); // 默认载入文本示例
        showMessage('文本对比工具已就绪，支持实时对比', 'success');
    }



    // 初始化所有工具
    initParentChildNavigation();
    initEncodingTools();
    initTimestampTool();
    initDateCalculatorTool();

    // 保持原有的文本转换工具初始化（向后兼容）
    const textConvertTool = document.getElementById('text-convert-tool');
    if (textConvertTool) {
        initTextConvertTool();
    }

    // 文本对比工具初始化
    const textCompareTool = document.getElementById('text-compare-tool');
    if (textCompareTool) {
        initTextCompareTool();
    }
});