# 文件路径: app/core/logic/datetime_converter.py

from datetime import datetime, timezone, timedelta
import dateparser
from dateparser.conf import Settings
import cn2an
import re
import sys

# 创建一个统一的、包含所有配置的 Settings 对象
UNIFIED_PARSER_SETTINGS = Settings()
UNIFIED_PARSER_SETTINGS.LANGUAGES = ['zh', 'en']
UNIFIED_PARSER_SETTINGS.TIMEZONE = 'UTC'
UNIFIED_PARSER_SETTINGS.RETURN_AS_TIMEZONE_AWARE = True


def log_message(message):
    """一个简单的日志函数，用于追踪。"""
    print(f"{message}", file=sys.stderr)


# --- VVV 这是本次修改的核心：为预处理器增加详细日志 VVV ---
def _preprocess_chinese_datetime_string(text: str) -> str:
    """
    对中文日期字符串进行预处理，以便dateparser能更好地解析。
    """
    log_message(f"--- [预处理日志 1/4] 接收到原始输入: '{text}'")

    # 1. 处理特殊的相对日期词汇
    replacements = {
        "大前天": "3天前", "前天": "2天前", "昨天": "1天前",
        "今天": "今天", "今晚": "今晚", "明天": "1天后",
        "后天": "2天后", "大后天": "3天后", "俩": "2",
    }
    processed_text = text
    for old, new in replacements.items():
        processed_text = processed_text.replace(old, new)
    log_message(f"--- [预处理日志 2/4] 特殊词汇替换后: '{processed_text}'")

    # 2. 使用 cn2an 将所有中文数字转为阿拉伯数字
    try:
        processed_text = cn2an.transform(processed_text, "cn2an")
        log_message(f"--- [预处理日志 3/4] cn2an数字转换后: '{processed_text}'")
    except Exception as e:
        log_message(f"--- [预处理日志 3/4] cn2an转换失败: {e}，跳过此步骤。")
        # 如果转换失败，继续使用之前的文本

    # 3. 处理上午/下午/晚上，并转换为24小时制
    def time_replacer(match):
        period = match.group(1)
        hour_str = match.group(2)
        hour = int(hour_str)

        if period == "下午" and hour < 12:
            hour += 12
        elif period == "晚上" and hour < 12:
            hour += 12

        return f" {hour}:00"

    processed_text = re.sub(r"(早上|中午|下午|晚上)\s*(\d{1,2})\s*点", time_replacer, processed_text)
    log_message(f"--- [预处理日志 4/4] 时间格式转换后，最终输出: '{processed_text}'")

    return processed_text


def convert_timestamp_to_datetime(timestamp_str: str) -> tuple[dict | None, str | None]:
    """将时间戳字符串转换为多种格式的日期时间。支持秒级、毫秒级、微秒级时间戳。"""
    if not timestamp_str.strip():
        return None, "时间戳输入为空"

    try:
        # 移除空格并检查是否为纯数字
        clean_timestamp = timestamp_str.strip()
        if not clean_timestamp.replace('.', '').isdigit():
            return None, "时间戳必须为数字格式"

        ts = float(clean_timestamp)
        timestamp_length = len(clean_timestamp.split('.')[0])  # 获取整数部分长度

        # 根据时间戳长度判断单位并转换为秒
        if timestamp_length == 10:
            # 秒级时间戳
            ts_seconds = ts
            timestamp_type = "秒级时间戳"
        elif timestamp_length == 13:
            # 毫秒级时间戳
            ts_seconds = ts / 1_000
            timestamp_type = "毫秒级时间戳"
        elif timestamp_length == 16:
            # 微秒级时间戳
            ts_seconds = ts / 1_000_000
            timestamp_type = "微秒级时间戳"
        elif timestamp_length < 10:
            return None, f"时间戳长度过短({timestamp_length}位)，请输入10位(秒)、13位(毫秒)或16位(微秒)时间戳"
        elif timestamp_length > 16:
            return None, f"时间戳长度过长({timestamp_length}位)，请输入10位(秒)、13位(毫秒)或16位(微秒)时间戳"
        else:
            return None, f"不支持的时间戳长度({timestamp_length}位)，请输入10位(秒)、13位(毫秒)或16位(微秒)时间戳"

        # 检查时间戳是否在合理范围内 (1970-2100年)
        if ts_seconds < 0 or ts_seconds > 4102444800:  # 2100年1月1日的时间戳
            return None, "时间戳超出有效范围(1970-2100年)"

        dt_utc = datetime.fromtimestamp(ts_seconds, tz=timezone.utc)
        dt_local = dt_utc.astimezone()

        result = {
            "timestamp_type": timestamp_type,
            "timestamp_in_seconds": int(ts_seconds),
            "timestamp_in_milliseconds": int(ts_seconds * 1000),
            "utc_datetime": dt_utc.strftime('%Y-%m-%d %H:%M:%S %Z'),
            "local_datetime": dt_local.strftime('%Y-%m-%d %H:%M:%S'),
            "local_datetime_with_weekday": dt_local.strftime('%Y年%m月%d日 %H:%M:%S (%A)'),
        }
        return result, None
    except ValueError as e:
        return None, f"时间戳格式错误: {e}"
    except OSError as e:
        return None, f"时间戳超出系统支持范围: {e}"
    except Exception as e:
        return None, f"转换失败: {e}"


def _normalize_datetime_string(datetime_str: str) -> str:
    """标准化日期时间字符串，支持多种分隔符和格式。"""
    import re

    # 移除多余空格
    normalized = datetime_str.strip()

    # 支持多种分隔符：将 / : 等替换为 -
    normalized = re.sub(r'[/:]', '-', normalized)

    # 处理年月格式 (如: 2025-06)
    if re.match(r'^\d{4}-\d{1,2}$', normalized):
        normalized += '-01 00:00:00'  # 补充为月初

    # 处理年月日格式 (如: 2025-06-27)
    elif re.match(r'^\d{4}-\d{1,2}-\d{1,2}$', normalized):
        normalized += ' 00:00:00'  # 补充时间为00:00:00

    # 处理年月日时格式 (如: 2025-06-27 19)
    elif re.match(r'^\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}$', normalized):
        normalized += ':00:00'  # 补充分秒

    # 处理年月日时分格式 (如: 2025-06-27 19:40)
    elif re.match(r'^\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}-\d{1,2}$', normalized):
        normalized = re.sub(r'(\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2})-(\d{1,2})$', r'\1:\2:00', normalized)

    # 处理完整格式但分隔符不标准的情况
    normalized = re.sub(r'(\d{4})-(\d{1,2})-(\d{1,2})\s+(\d{1,2})-(\d{1,2})-(\d{1,2})',
                       r'\1-\2-\3 \4:\5:\6', normalized)

    return normalized


def convert_datetime_to_timestamp(datetime_str: str) -> tuple[dict | None, str | None]:
    """将日期时间字符串转换为时间戳。支持多种日期格式和分隔符。"""
    if not datetime_str.strip():
        return None, "日期字符串输入为空"

    try:
        # 先进行格式标准化
        normalized_str = _normalize_datetime_string(datetime_str)
        log_message(f"标准化后的日期字符串: '{normalized_str}'")

        # 再进行中文预处理
        processed_str = _preprocess_chinese_datetime_string(normalized_str)
        log_message(f"预处理后的日期字符串: '{processed_str}'")

        # 使用dateparser解析
        dt_object = dateparser.parse(processed_str, settings=UNIFIED_PARSER_SETTINGS)

        if dt_object is None:
            # 尝试直接解析标准化后的字符串
            try:
                from datetime import datetime
                # 尝试常见格式
                formats = [
                    '%Y-%m-%d %H:%M:%S',
                    '%Y-%m-%d %H:%M',
                    '%Y-%m-%d %H',
                    '%Y-%m-%d',
                    '%Y-%m'
                ]

                for fmt in formats:
                    try:
                        dt_object = datetime.strptime(normalized_str, fmt)
                        break
                    except ValueError:
                        continue

                if dt_object is None:
                    return None, f"无法解析日期格式: '{datetime_str}'。支持格式: YYYY-MM-DD HH:MM:SS、YYYY/MM/DD、YYYY-MM等"

            except Exception:
                return None, f"无法解析日期格式: '{datetime_str}'。支持格式: YYYY-MM-DD HH:MM:SS、YYYY/MM/DD、YYYY-MM等"

        timestamp = int(dt_object.timestamp())

        result = {
            "original_input": datetime_str,
            "parsed_datetime": dt_object.strftime('%Y-%m-%d %H:%M:%S'),
            "timestamp_in_seconds": timestamp,
            "timestamp_in_milliseconds": timestamp * 1000
        }

        return result, None

    except Exception as e:
        return None, f"转换失败: {e}"


def calculate_date_difference(start_date_str: str, end_date_str: str) -> tuple[str | None, str | None]:
    """计算两个日期之间的差值。"""
    log_message(f"进入 calculate_date_difference, start: '{start_date_str}', end: '{end_date_str}'")
    if not start_date_str.strip() or not end_date_str.strip(): return None, "起始或结束日期不能为空。"
    try:
        processed_start = _preprocess_chinese_datetime_string(start_date_str)
        processed_end = _preprocess_chinese_datetime_string(end_date_str)

        start_dt = dateparser.parse(processed_start, settings=UNIFIED_PARSER_SETTINGS)
        end_dt = dateparser.parse(processed_end, settings=UNIFIED_PARSER_SETTINGS)

        if not start_dt or not end_dt: return None, "无法解析日期格式。"

        if end_dt < start_dt:
            start_dt, end_dt = end_dt, start_dt

        diff = end_dt - start_dt
        days = diff.days
        seconds = diff.seconds
        hours, remainder = divmod(seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        result = f"相差: {days} 天, {hours} 小时, {minutes} 分钟, {seconds} 秒"
        log_message(f"计算成功: {result}")
        return result, None
    except Exception as e:
        log_message(f"计算失败: {e}")
        return None, f"计算失败: {e}"


def calculate_date_offset(start_date_str: str, days_offset: int) -> tuple[str | None, str | None]:
    """计算日期偏移。"""
    log_message(f"进入 calculate_date_offset, start: '{start_date_str}', days: {days_offset}")
    if not start_date_str.strip(): return None, "起始日期不能为空。"
    try:
        processed_start = _preprocess_chinese_datetime_string(start_date_str)
        start_dt = dateparser.parse(processed_start, settings=UNIFIED_PARSER_SETTINGS)
        if not start_dt: return None, "无法解析起始日期格式。"

        end_dt = start_dt + timedelta(days=days_offset)
        result = end_dt.astimezone().strftime('%Y-%m-%d %H:%M:%S')
        log_message(f"计算成功: {result}")
        return result, None
    except OverflowError:
        log_message("计算失败: OverflowError")
        return None, "输入的偏移天数过大，已超出可计算范围。"
    except Exception as e:
        log_message(f"计算失败: {e}")
        return None, f"计算失败: {e}"