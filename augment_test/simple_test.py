# -*- coding: utf-8 -*-
"""
简单测试时间转换功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from app.core.logic.datetime_converter import convert_timestamp_to_datetime, convert_datetime_to_timestamp
    print("成功导入模块")
    
    # 测试时间戳转换
    print("\n=== 测试时间戳转换 ===")
    result, error = convert_timestamp_to_datetime("1640995200")
    if error:
        print(f"错误: {error}")
    else:
        print(f"成功: {result}")
    
    # 测试日期转换
    print("\n=== 测试日期转换 ===")
    result, error = convert_datetime_to_timestamp("2025-06-27 19:40:25")
    if error:
        print(f"错误: {error}")
    else:
        print(f"成功: {result}")
        
    print("\n测试完成")
    
except Exception as e:
    print(f"导入或运行出错: {e}")
    import traceback
    traceback.print_exc()
