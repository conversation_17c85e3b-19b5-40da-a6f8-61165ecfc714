# -*- coding: utf-8 -*-
"""
测试时间转换工具的改进功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.core.logic.datetime_converter import convert_timestamp_to_datetime, convert_datetime_to_timestamp


def test_timestamp_to_datetime():
    """测试时间戳转日期功能"""
    print("=== 测试时间戳转日期功能 ===")
    
    test_cases = [
        ("1640995200", "10位秒级时间戳"),
        ("1640995200000", "13位毫秒级时间戳"),
        ("1640995200000000", "16位微秒级时间戳"),
        ("164099520", "9位时间戳(应该失败)"),
        ("16409952000000000", "17位时间戳(应该失败)"),
        ("abc123", "非数字时间戳(应该失败)"),
        ("", "空字符串(应该失败)"),
    ]
    
    for timestamp_str, description in test_cases:
        print(f"\n测试: {description}")
        print(f"输入: '{timestamp_str}'")
        result, error = convert_timestamp_to_datetime(timestamp_str)
        
        if error:
            print(f"错误: {error}")
        else:
            print(f"成功: {result}")


def test_datetime_to_timestamp():
    """测试日期转时间戳功能"""
    print("\n\n=== 测试日期转时间戳功能 ===")
    
    test_cases = [
        ("2025-06-27 19:40:25", "完整格式(-)"),
        ("2025/06/27 19:40:25", "完整格式(/)"),
        ("2025-06-27 19:40", "日期时分"),
        ("2025/06/27 19:40", "日期时分(/)"),
        ("2025-06-27 19", "日期小时"),
        ("2025/06/27 19", "日期小时(/)"),
        ("2025-06-27", "仅日期(-)"),
        ("2025/06/27", "仅日期(/)"),
        ("2025-06", "年月(-)"),
        ("2025/06", "年月(/)"),
        ("2025:06:27 19:40:25", "混合分隔符"),
        ("invalid-date", "无效日期(应该失败)"),
        ("", "空字符串(应该失败)"),
    ]
    
    for datetime_str, description in test_cases:
        print(f"\n测试: {description}")
        print(f"输入: '{datetime_str}'")
        result, error = convert_datetime_to_timestamp(datetime_str)
        
        if error:
            print(f"错误: {error}")
        else:
            print(f"成功: {result}")


def test_current_timestamp_examples():
    """测试当前时间戳示例"""
    print("\n\n=== 测试当前时间戳示例 ===")
    
    import time
    from datetime import datetime
    
    # 获取当前时间戳
    now = datetime.now()
    current_seconds = int(now.timestamp())
    current_milliseconds = int(now.timestamp() * 1000)
    
    print(f"当前秒级时间戳: {current_seconds}")
    print(f"当前毫秒级时间戳: {current_milliseconds}")
    
    # 测试转换
    print("\n测试秒级时间戳转换:")
    result, error = convert_timestamp_to_datetime(str(current_seconds))
    if error:
        print(f"错误: {error}")
    else:
        print(f"转换结果: {result}")
    
    print("\n测试毫秒级时间戳转换:")
    result, error = convert_timestamp_to_datetime(str(current_milliseconds))
    if error:
        print(f"错误: {error}")
    else:
        print(f"转换结果: {result}")


if __name__ == "__main__":
    test_timestamp_to_datetime()
    test_datetime_to_timestamp()
    test_current_timestamp_examples()
    print("\n=== 测试完成 ===")
