<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript语法测试</title>
</head>
<body>
    <h1>JavaScript语法测试</h1>
    <div id="test-output"></div>
    
    <script>
        // 测试JavaScript语法
        try {
            // 模拟main.js中的关键函数
            function testTimestampTool() {
                const now = new Date();
                const timestampSeconds = Math.floor(now.getTime() / 1000);
                const timestampMilliseconds = now.getTime();
                
                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const seconds = String(now.getSeconds()).padStart(2, '0');
                const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
                
                const dateTimeFullStr = `${year}年${month}月${day}日${hours}点${minutes}分${seconds}秒`;
                const dateTimeMillisecondsStr = `${year}年${month}月${day}日${hours}点${minutes}分${seconds}秒${milliseconds}毫秒`;
                
                return {
                    timestampSeconds,
                    timestampMilliseconds,
                    dateTimeFullStr,
                    dateTimeMillisecondsStr
                };
            }
            
            const result = testTimestampTool();
            document.getElementById('test-output').innerHTML = `
                <h2>测试成功！</h2>
                <p><strong>当前秒级时间戳:</strong> ${result.timestampSeconds}</p>
                <p><strong>当前毫秒级时间戳:</strong> ${result.timestampMilliseconds}</p>
                <p><strong>当前年月日时分秒:</strong> ${result.dateTimeFullStr}</p>
                <p><strong>当前年月日时分秒毫秒:</strong> ${result.dateTimeMillisecondsStr}</p>
            `;
            
            console.log('JavaScript语法测试通过');
            
        } catch (error) {
            document.getElementById('test-output').innerHTML = `
                <h2 style="color: red;">测试失败！</h2>
                <p><strong>错误信息:</strong> ${error.message}</p>
                <pre>${error.stack}</pre>
            `;
            console.error('JavaScript语法测试失败:', error);
        }
    </script>
</body>
</html>
